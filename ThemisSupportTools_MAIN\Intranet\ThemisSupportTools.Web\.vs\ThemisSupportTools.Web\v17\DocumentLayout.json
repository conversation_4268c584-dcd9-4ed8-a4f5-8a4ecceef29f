{"Version": 1, "WorkspaceRootPath": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\transferepointagephoto\\transferepointagephoto.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\transferepointagephoto\\transferepointagephoto.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\managers\\tst\\themissupporttoolsmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\abonnementmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\wsadmin\\wsadminstructuresmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\services\\access\\tstaccessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\serviceinclusionexclusion\\serviceinclusionexclusion.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\serviceinclusionexclusion\\serviceinclusionexclusion.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\serviceinclusionexclusion\\serviceinclusionexclusion.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\serviceinclusionexclusion\\serviceinclusionexclusion.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\web.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\resources\\resource.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\resources\\resource.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\couponspromo\\couponspromorepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\libraries_sqlscripts\\tst\\couponspromo\\getcouponspromo.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\automapperprofiles\\themissupporttoolsprofiles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManagerOld.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.dto\\tst\\configinimodels\\configinimodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.dto\\tst\\configinisectiondto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.local.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.local.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\managers\\tst\\interfaces\\ithemissupporttoolsmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_main\\themissupporttools_main\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_main\\themissupporttools_main\\libraries\\core.themis.libraries.dto\\wsadmin\\wsadminstructuredto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "TstAccessService.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "ViewState": "AgIAAL8AAAAAAAAAAAAQwJUAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-11T15:37:09.447Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "WsAdminStructuresManager.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\WsAdmin\\WsAdminStructuresManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\WsAdmin\\WsAdminStructuresManager.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\WsAdmin\\WsAdminStructuresManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\WsAdmin\\WsAdminStructuresManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T08:13:26.335Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "GestionMaquetteAboFermer.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAAwHEAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T13:10:43.913Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AbonnementManager.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "ViewState": "AgIAAHkAAAAAAAAAAAAuwI4AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T13:13:39.15Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ThemisSupportToolsManager.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "ViewState": "AgIAAGkGAAAAAAAAAAAuwH4GAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-11T09:15:21.034Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "TransferePointagePhoto.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "ViewState": "AgIAAE4BAAAAAAAAAAAUwGABAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T13:50:45.2Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ServiceInclusionExclusion.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor.cs", "ViewState": "AgIAABQAAAAAAAAAAAAIwPUAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-12T06:50:40.511Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{99b8fa2f-ab90-4f57-9c32-949f146f1914}"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ServiceInclusionExclusion.razor", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ServiceInclusionExclusion\\ServiceInclusionExclusion.razor", "ViewState": "AgIAABMBAAAAAAAAAAAawK8BAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-09-12T06:50:36.64Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.json", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.json", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.json", "ViewState": "AgIAABIAAAAAAAAAAAAAABwAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-12T12:57:34.356Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "web.config", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\web.config", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\web.config", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\web.config", "RelativeToolTip": "ThemisSupportTools.Web\\web.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-09-11T15:49:52.198Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "CouponsPromo.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "ViewState": "AgIAADkBAAAAAAAAAAAIwEwBAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-11T09:11:54.34Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "CouponsPromo.razor", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "ViewState": "AgIAACQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-11T09:11:47.05Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "getCouponsPromo.sql", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\TST\\CouponsPromo\\getCouponsPromo.sql", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\TST\\CouponsPromo\\getCouponsPromo.sql", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\TST\\CouponsPromo\\getCouponsPromo.sql", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\TST\\CouponsPromo\\getCouponsPromo.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-09-09T15:07:28.658Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "Resource.Designer.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "ViewState": "AgIAAD4AAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-11T15:36:30.5Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CouponsPromoRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "ViewState": "AgIAACMAAAAAAAAAAAAmwDgAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-11T09:16:00.95Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "ThemisSupportToolsProfiles.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-09T13:13:55.367Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "ThemisSupportToolsManagerOld.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManagerOld.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManagerOld.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManagerOld.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManagerOld.cs", "ViewState": "AgIAAL8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-09T13:20:12.801Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "ConfigIniModel.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniModels\\ConfigIniModel.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniModels\\ConfigIniModel.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniModels\\ConfigIniModel.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniModels\\ConfigIniModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-09T13:20:19.454Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "ConfigIniSectionDTO.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniSectionDTO.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniSectionDTO.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniSectionDTO.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\TST\\ConfigIniSectionDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-09-09T13:20:22.671Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "GestionMaquetteAboFermer.razor", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-12T13:10:41.232Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "launchSettings.json", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Properties\\launchSettings.json", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Properties\\launchSettings.json", "RelativeToolTip": "ThemisSupportTools.Web\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-12T12:57:16.428Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "IThemisSupportToolsManager.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\Interfaces\\IThemisSupportToolsManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\Interfaces\\IThemisSupportToolsManager.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\Interfaces\\IThemisSupportToolsManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\Interfaces\\IThemisSupportToolsManager.cs", "ViewState": "AgIAADEAAAAAAAAAAIA1wDwAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T13:44:43.729Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "appsettings.local.json", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.local.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.local.json", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.local.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.local.json", "ViewState": "AgIAABIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-12T12:56:50.172Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "appsettings.Staging.json", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.Staging.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.Staging.json", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.Staging.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.Staging.json", "ViewState": "AgIAAAwAAAAAAAAAAAAAACYAAAB1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-12T12:37:16.553Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "WsAdminStructureDTO.cs", "DocumentMoniker": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\WSAdmin\\WsAdminStructureDTO.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\WSAdmin\\WsAdminStructureDTO.cs", "ToolTip": "D:\\WORK\\TST_MAIN\\ThemisSupportTools_MAIN\\Libraries\\Core.Themis.Libraries.DTO\\WSAdmin\\WsAdminStructureDTO.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\WSAdmin\\WsAdminStructureDTO.cs", "ViewState": "AgIAABQAAAAAAAAAAAAuwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T09:02:36.626Z"}]}]}]}