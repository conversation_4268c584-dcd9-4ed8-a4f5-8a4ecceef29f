/*CrossSelling @listEventsId=@pListEventsId, @listSessionsId=@pListSessionsId, @languageId=0, @paId=@pBuyerProfilId, @pPoidsGenre=@pPoidsGenre,
 @pPoidsSousGenre=@pPoidsSousGenre, @pPoidsCible=@pPoidsCible, @pPoidsMois=@pPoidsMois,@pDifferenceDate=@pDifferenceDate,@pPoidsgroupe=@pPoidsgroupe,
 @pNombreARemonter=@pNombreARemonter, @pLangCode=@pLangCode

*/

/*
 @listEventsId varchar(50) =@pListEventsId
 @listSessionsId varchar(50) = @pListSessionsId
 @paId int = @pBuyerProfilId
 @pPoidsGenre int = @pPoidsGenre
 @pPoidsSousGenre int = @pPoidsSousGenre
 @pPoidsCible int = @pPoidsCible
 @pPoidsMois int = @pPoidsMois
 @pDifferenceDate int = @pDifferenceDate
 @pPoidsgroupe int = @pPoidsgroupe
 @pNombreARemonter int = @pNombreARemonter
 @pLangCode varchar(5) = @pLangCode

*/
 
 
 /*CAS-86891-L3V7B4
Dès qu'un des événements de la liste ci-dessous est mis au panier, alors les événéments apparaissent dans le cross-seling :

- id event=3037
- idevent=3164
- idevent=3165
- idevent=3171

- EventId=3174
- EventId=3173
- EventId=3183

 */
 
DECLARE @tabl TABLE (id int,poids int) DECLARE @id int, @weight_sum int, @weight_point int DECLARE @poidsGenre int, 
@poidsSousGenre int, @poidsMois int,@poidsgroupe int, @differenceDate int, @poidsCible int, @nombreARemonter int;

set @poidsGenre = @pPoidsGenre;  
set @poidsSousGenre = @pPoidsSousGenre; 
set @poidsCible =@pPoidsCible; 
set @poidsMois =@pPoidsMois; 
set @differenceDate = @pDifferenceDate;
set @poidsgroupe = @pPoidsgroupe;  
set @nombreARemonter = @pNombreARemonter;  



/* mettre en table temp les ids de manifs recus en parametre */ 

/* mettre en table temp les ids de manifs recus en parametre */     
DECLARE @TempList table     ( EventID int ) 
DECLARE @EventID varchar(10), @Pos int 
    
declare @sListEventsId varchar(50)

SET @sListEventsId =  LTRIM(RTRIM(@listEventsId))+ ',' SET @Pos = CHARINDEX(',', @sListEventsId, 1)   
SET @Pos = CHARINDEX(',', @sListEventsId, 1)   
IF REPLACE(@sListEventsId, ',', '') <> '' 
BEGIN      

    WHILE @Pos > 0      
    BEGIN           
        SET @EventID = LTRIM(RTRIM(LEFT(@sListEventsId, @Pos - 1)))          
        IF @EventID <> ''           
        BEGIN               
            INSERT INTO @TempList (EventID) VALUES (CAST(@EventID AS int))              
        END             
        SET @sListEventsId = RIGHT(@sListEventsId, LEN(@sListEventsId) - @Pos)             
        SET @Pos = CHARINDEX(',', @sListEventsId, 1)         
    END 
END   /* table des ids de manifs Ã  proposer */     

DECLARE @TempListToShow table   (EventID int)  /* job principal */ 

INSERT @tabl select manifestation_id, similitude_groupe * @poidsgroupe + similitude_sous_genre * @poidsSousGenre + similitude_genre * @poidsGenre  + similitude_date * @poidsMois + similitude_cible * @poidsCible + 1 as poids  
FROM  ( 
    SELECT      /******* cible */   similitude_cible =      case when   (select top 1 POWER(2,sc.Cible_id) as poids from Seance_Cible sc, seance s          
    where s.Seance_id = sc.Seance_id and s.manifestation_id =m.manifestation_id         ) in (select top 1 POWER(2,sc.Cible_id) as poids from Seance_Cible sc, seance s         
    where s.Seance_id = sc.Seance_id and s.manifestation_id in (select * from @TempList)) then 1 else 0 end         ,       
    /******* groupe */  similitude_groupe =     case when manifestation_groupe_id  in (
    select manifestation_groupe_id from manifestation where manifestation_id in (select * from @TempList)) then 1 else 0 end        ,manifestation_groupe_id as groupe_id,      
    /****** sous genre */   similitude_sous_genre =     case when id_genre in (select ID_genre from manifestation where manifestation_id in (select * from @TempList)) then 1 else 0 end    ,
    /****** genre */  id_genre as id_sous_genre , (select top 1 g.id as genre_id from manifestation_genre sg, manifestation_groupe_genre g, 
    manifestation m2 where sg.groupe_id=g.id    and sg.id=m.ID_genre and m2.manifestation_id =m.manifestation_id) as genre_id,      
    similitude_genre =  case when   (select distinct g.id as genre_id                       
            FROM manifestation_genre sg, manifestation_groupe_genre g  
            WHERE sg.groupe_id=g.id AND sg.id in (select id_genre from manifestation m2 where m2.manifestation_id=m.manifestation_id)   )   
            in (SELECT /* les genres des manifs passÃ©es en parametres */   
                distinct g.id as genre_id 
                FROM manifestation_genre sg, manifestation_groupe_genre g   
                WHERE sg.groupe_id=g.id                                         
                AND sg.id in (select id_genre from manifestation where manifestation_id in (select * from @TempList))   ) 
            then 1 else 0 end,  
    /*** date de seance */      
    similitude_date = case when abs(datediff(day,   (select top 1 s.seance_date_deb from seance s, gestion_place gp     
    where s.seance_Id =gp.seance_id and gp.manif_id=m.manifestation_id),    
    (select top 1 s.seance_date_deb from seance s, gestion_place gp     
    where s.seance_Id =gp.seance_id and gp.manif_id in (@listEventsId))))<=@differenceDate then 1 else 0 end,  
    m.* from manifestation m where manifestation_id not in (select * from @TempList)   and manifestation_id in (
    select distinct manif_id from gestion_place gp where formule_id is null and dispo>0 and isvalide=1) ) ss  
    order by poids desc  


/* ************ supp les manifs qui ne sont pas possibles (profil acheteur ou non) */
if (@paId =0)
begin
    delete @tabl WHERE id not in ( select manif_id from 
    gestion_place gp WHERE isContrainteIdentite =0
      and formule_id is null and dispo>0 and isvalide=1 and categ_id is not null)
end

else
begin
    delete @tabl WHERE id not in ( select manif_id from 
    gestion_place gp 
    inner join offre_gestion_place ogp on gp.gestion_place_id = ogp.gestion_place_id
    inner join offre o on o.offre_id = ogp.offre_id
    inner join offre_profil_acheteur opa on opa.offre_id = o.offre_id
    where opa.profil_acheteur_id =@paId  and formule_id is null and dispo>0 and isvalide=1 and categ_id is not null )
end

--select * from @tabl;

declare @nbrEventsPossibles int
select @nbrEventsPossibles =count(*) from @tabl;

    /**** select ******** */ 
	
	
declare @myPanierContainsManifParticulieres int
select @myPanierContainsManifParticulieres = count(*) from @TempList where eventid in (3037,3164,3165,3171,3174,3173,3183)
if (@myPanierContainsManifParticulieres> 0)
begin

	--update @tabl set poids = 3000 where id in ( 3037,3164,3165,3171,3174,3173,3183)
	delete @tabl where id not in (3037,3164,3165,3171,3174,3173,3183)
end
	
    
SELECT @weight_sum = SUM(poids) FROM @tabl;  
DECLARE @n int, @ntotal int set @n=0; set @ntotal=0; 

WHILE @n<@nombreARemonter AND @ntotal<100 and @n<@nbrEventsPossibles /* pour 100 iteration max */ 
BEGIN   
    SELECT @weight_point = ROUND(((@weight_sum - 1) * RAND() + 1), 0)  
    
    SELECT @id = CASE WHEN @weight_point < 0 THEN @id ELSE [table].id END,  
            @weight_point = @weight_point - [table].poids   
    FROM    @tabl [table]   

    IF not exists(select 1 from @TempListToShow where EventID=@id)  
    BEGIN
        INSERT @TempListToShow values(@id);     
        SET @n=@n+1;        
    END     
    SET @ntotal=@ntotal+1; 
END   

    SELECT m.manifestation_id as eventid, m.manifestation_descrip as eventname , 
    case when manifestation_groupe_genre.nom  is null then 'Autre' else manifestation_groupe_genre.nom end AS genre,   convert(varchar,   
    (select top 1 s.seance_date_deb from seance s, VueGestionPlace gp   where s.seance_Id =gp.seance_id and gp.manif_id=m.manifestation_id)     ,126    )    
    as seance_date_deb, 
    CASE WHEN tm.manifestation_nom IS NULL THEN m.manifestation_nom  else tm.manifestation_nom end as manifestation_nom,
    
    CASE WHEN (select top 1 s.NEPASAFFICHERDATE from seance s, VueGestionPlace gp   where s.seance_Id =gp.seance_id and gp.manif_id=m.manifestation_id)  like 'O' THEN 0 ELSE 1 END as isShowSessionDate,
    CASE WHEN (select top 1 s.NEPASAFFICHERDATE from seance s, VueGestionPlace gp   where s.seance_Id =gp.seance_id and gp.manif_id=m.manifestation_id)  like 'H' THEN 0 WHEN (select top 1 s.NEPASAFFICHERDATE from seance s, VueGestionPlace gp   where s.seance_Id =gp.seance_id and gp.manif_id=m.manifestation_id)  like 'O' THEN 0 ELSE 1 END as isShowSessionHour
    , *
    from  manifestation m 
    left join traduction_manifestation tm on tm.manifestation_id = m.manifestation_id and tm.langue_id = (select langue_id from langue where langue_code = @pLangCode)  
    LEFT JOIN manifestation_genre ON m.ID_genre = manifestation_genre.id 
    LEFT JOIN manifestation_groupe_genre ON manifestation_genre.id = manifestation_groupe_genre.id AND manifestation_genre.groupe_id = manifestation_groupe_genre.id  
    WHERE m.manifestation_id in (select EventID FROM @TempListToShow)       
    




