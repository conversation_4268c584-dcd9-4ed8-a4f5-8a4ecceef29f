﻿/*declare @pIdentiteId int
declare @pStructureId int

set @pIdentiteId = 473
*/

SELECT * FROM (
  SELECT  donneur_ordre_identite_id,  consomateur_identite_id, identite_id,  identite_nom, identite_prenom, postal_rue1, postal_rue2, postal_rue3, postal_rue4, postal_cp,
  postal_region, postal_pays, postal_ville, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7,appellation_id,identite_date_naissance,FicheSupprimer, filiere_id
   ,(case when  i.identite_id = @pIdentiteId then 1 else 0 end) as isMineIdentity
   FROM groupe_consomateur gc
  inner join identite i on i.identite_id=gc.consomateur_identite_id
   WHERE  i.FicheSupprimer <> 'O' AND donneur_ordre_identite_id=@pIdentiteId 
  UNION
  SELECT @pIdentiteId  as donneur_ordre_identite_id, @pIdentiteId  as consomateur_identite_id, identite_id,  identite_nom, identite_prenom, postal_rue1, postal_rue2, postal_rue3, postal_rue4, postal_cp,
  postal_region, postal_pays, postal_ville, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7,appellation_id,identite_date_naissance,FicheSupprimer, filiere_id
   ,(case when  i.identite_id = @pIdentiteId then 1 else 0 end) as isMineIdentity
   FROM identite i 
   WHERE  i.FicheSupprimer <> 'O' AND i.identite_id=@pIdentiteId  
   UNION
   SELECT @pIdentiteId as donneur_ordre_identite_id, @pIdentiteId   as consomateur_identite_id, identite_id,  identite_nom, identite_prenom, postal_rue1, postal_rue2, postal_rue3, postal_rue4, postal_cp,
  postal_region, postal_pays, postal_ville, postal_tel1,postal_tel2,postal_tel3,postal_tel4,postal_tel5,postal_tel6,postal_tel7,appellation_id,identite_date_naissance,FicheSupprimer, filiere_id
   ,(case when  i.identite_id = @pIdentiteId then 1 else 0 end) as isMineIdentity
   FROM identite i 
   WHERE  i.FicheSupprimer <> 'O' AND 
   i.identite_id=@pIdentiteId  

) AS identconsom
ORDER BY  isMineIdentity desc, identite_nom, identite_prenom