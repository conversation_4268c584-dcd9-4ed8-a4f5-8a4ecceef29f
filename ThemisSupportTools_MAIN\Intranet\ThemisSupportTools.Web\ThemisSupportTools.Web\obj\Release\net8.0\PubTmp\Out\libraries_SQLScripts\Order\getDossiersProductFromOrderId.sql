﻿declare @myOrderId int = @pOrderId

select
dos_prod_id,
dp.produit_id, 
dos_prod_etat,
dos_prod_numero,
manifestation_id,
seance_id,
num_paiement,
nb_produit,
date_operation,
dos_prod_montant1,
p.produit_nom,
p.pref_affichage,
p.unbilletparproduit,
convert(int,dos_prod_montant1 * 100) as dossier_amount,
convert(int,p.vts_grille1 * 100) as produit_amount,
convert(int,dos_prod_montant2 * 100) as dossier_feeamount,
convert(int,p.vts_grille2 * 100) as produit_feeamount,

p.groupe_id,
case p.groupe_id 
	when  12 then 'PRESA' 
	when  1 then 'MO'
	when  6 then 'MO'
	when  7 then 'ASS'
	else 'PROD' 
end as type_ligne

 FROM dossier_produit dp
INNER JOIN produit p on p.produit_id = dp.produit_id
 WHERE commande_id = @myOrderId