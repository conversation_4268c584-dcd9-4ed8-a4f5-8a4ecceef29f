﻿declare @myOrderId int

select 
@myOrderId = dp.commande_id
 from dossier_produit dp 
	inner join dossier_produit_reservation presa on presa.dos_prod_id = dp.dos_prod_id
	inner join produit prod on prod.produit_id = dp.produit_id
where commande_id = @order_id
and dp.dos_prod_etat ='P'
and presa.date_limite > GETDATE()

if (@myOrderId = @order_id)
	select 1
else
begin
	declare @dossier_produit_reservation_exist int
	select @dossier_produit_reservation_exist = count(*) /* si dossier_produit_reservation = null => commande rodrigue */
		 from dossier_produit dp 
			inner join dossier_produit_reservation presa on presa.dos_prod_id = dp.dos_prod_id
			inner join produit prod on prod.produit_id = dp.produit_id
		where commande_id = @order_id
	if (@dossier_produit_reservation_exist = 0)
		select 1
	else
		select 0

 end