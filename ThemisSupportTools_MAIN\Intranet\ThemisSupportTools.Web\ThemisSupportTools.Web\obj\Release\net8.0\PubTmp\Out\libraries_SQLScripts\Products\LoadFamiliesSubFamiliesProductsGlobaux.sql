/* LoadFamiliesSubFamiliesProductsGlobaux.sql : les produits globaux au panier */
/*
declare @plangcode varchar(2) ='fr'
*/
		
DECLARE @langue_Id int = 0
SELECT @langue_Id = langue_id FROM langue WHERE  upper(langue_code) = upper(@plangcode);
IF @langue_Id is null
	set @langue_Id = 0
 
 DECLARE @Prod table (FamilyId int, FamilyName varchar(250), FamilyCode varchar(250), FamilyColor int, FamilyMasked bit, FamilyPref_Affichage int, SubFamilyId int, SubFamilyName varchar(250)
	,SubFamilyCode varchar(250),SubFamilyColor int, SubFamilyPref_affichage int, SubFamilyMasked bit, 
	productId int 
	,productName varchar(250) ,productCode varchar(250),Maquettebillet_id int ,Produit_V int ,ProductDesc varchar(500),totalamount int,
	Nb_Min int, Nb_Max int, Restant int, Type_Montant int, Groupe_id int, Charge int, Pref_Affichage int, step float, descriptif_long_text varchar(max))

INSERT INTO @Prod 

SELECT -1 as familleId,  'noname' as familleNom, 'noname' as familleCode, '' as familleCouleur,
	'false' as familleMasquer, 0 as familleOrdreAffichage,
	-1 as sousFamilleId, 'noname' as sousFamilleNom, 'noname' as sousFamilleCode,
	'' as sousFamilleCouleur, 0 as sousFamilleOrdreAffichage, 'false' as sousFamilleMasquer,
	p.produit_id, 
	isnull(tpi.produit_nom, p.produit_nom)  as produit_nom,	
	p.produit_code, p.maquettebillet_id, p.produit_v, p.produit_descrip, ((montant1+montant2)*100) as totalAmountInCent,
	nb_min, nb_max, restant, p.type_montant, p.groupe_id, (montant2 *100) as chargeInCent,p.pref_affichage, step,  pdl.descriptif_long_text
	FROM produit_internet p_i
	INNER JOIN produit p on p.produit_id = p_i.produit_id
	LEFT JOIN traduction_produit tpi on p.produit_id=tpi.produit_id and langue_id=@langue_Id
	INNER JOIN produit_stock ps ON ps.produit_id=p.produit_id
	LEFT OUTER JOIN produit_descriptif_long pdl ON pdl.Produit_id = p.produit_id
	WHERE acces_autonome=0 AND
	p.produit_type = 0 /* est de type stock global */	
	AND date_deb_validite<getdate() AND date_fin_validite>getdate() 
	AND site_vente=1 AND p.groupe_id not in (6, 7, 12) and p.produit_anu <> 99
	AND ps.restant>0
	AND p.produit_id not in (select produit_id from Produit_Lien_Sous_Famille)
	AND ps.manifestation_id = 0

	SELECT 
		familyid, familyname, familycode, familycolor, familymasked, familypref_affichage, subfamilyid, subfamilyname,
		subfamilycode, subfamilycolor, subfamilypref_affichage, subfamilymasked, productid, productname, productcode, pw.maquettebillet_id,
		pw.produit_v, productdesc, totalamount, nb_min, 
		nb_max, pw.type_montant,pw.groupe_id, charge, 
		isnull(tva.tva_libelle,'') as tva_libelle , isnull(tva.tva_taux,0) as tva_taux,
		pw.pref_affichage, step,
		MIN(Restant) as restant,
		'' as Groupe_Code,			
		0 as catId,
		'' as catName,
		1 as CatalogSellable,
		null as CatalogDate_end_utilisation 
	,ISNULL(pw.descriptif_long_text, '') as descriptif_long_text
	 FROM @Prod pw
	 INNER JOIN produit p ON pw.productId = p.produit_id
	 LEFT OUTER JOIN tva ON  tva.tva_id = p.tva1

	 GROUP BY 
	 	familyid, familyname, familycode, familycolor, familymasked, familypref_affichage, subfamilyid, subfamilyname,
		subfamilycode, subfamilycolor, subfamilypref_affichage, subfamilymasked, productid, productname, productcode, pw.maquettebillet_id,
		pw.produit_v, productdesc, totalamount, nb_min, nb_max, pw.type_montant,pw.groupe_id, charge, pw.pref_affichage, 
		tva.tva_libelle, tva.tva_taux,
		step, descriptif_long_text


