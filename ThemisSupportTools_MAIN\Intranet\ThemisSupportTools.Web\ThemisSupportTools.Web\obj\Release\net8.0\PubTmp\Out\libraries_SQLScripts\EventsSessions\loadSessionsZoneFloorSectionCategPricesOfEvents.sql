/* grille de tarif tout simple pour un evenement */


declare @langCode varchar(5)
set @langCode=@plangCode
declare @langId int
set @langId =0;
select @langId = langue_id FROM langue WHERE langue_code=@langCode

declare @mysessions table (seance_id int)

insert into @mysessions
	select seance_id FROM seance WHERE manifestation_id = [eventID] AND seance_masquer <> 'O' AND seance_cloturer <> 'O' AND seance_verrouiller <> 'O' AND supprimer <> 'O'



SELECT count(*) as totalSeatsCount,
categoryid, catdisplayOrder, tarifdisplayOrder, sessionid, categname, pricename, priceid, zoneId, floorId, sectionid, zoneName, floorName, sectionName, RailingPriceID, AmountExceptTax, Charge,tax,Discount, Commission,
TotalTax, TotalAmount, TicketAmount, sessionStartDate, sessionEndDate, afficherLaDate
FROM (

SELECT cat.categ_id as categoryid, cat.pref_affichage as catdisplayOrder, tt.type_tarif_id as priceid, tt.pref_affichage as tarifdisplayOrder, s.seance_id as sessionid,

case WHEN tcat.categ_id is null THEN cat.categ_nom ELSE tcat.categ_nom END as categName,
case WHEN ttt.type_tarif_id is null THEN tt.type_tarif_nom ELSE ttt.type_tarif_nom END as priceName,

z.zone_id as zoneId,
et.etage_id as floorId,
sect.section_id as sectionId,
case WHEN tzone.zone_id is null THEN z.zone_nom ELSE tzone.zone_nom END as zoneName,
case WHEN tetage.etage_id is null THEN et.etage_nom ELSE tetage.etage_nom END as floorName,
case WHEN tsection.section_id is null THEN tsection.section_nom ELSE sect.section_nom END as sectionName,
vts.vts_id as RailingPriceID
,vts.vts_grille1 as AmountExceptTax
,vts.vts_grille2 as Charge 
,Tax= case when modecol4='TAXE' then vts.vts_grille4 else 0 END + case  when modecol5='TAXE' then vts.vts_grille5 else 0 END + case  when modecol6='TAXE' then vts.vts_grille6 else 0 END + case  when modecol7='TAXE' then vts.vts_grille7 else 0 END + case  when modecol8='TAXE' then vts.vts_grille8 else 0 END + case  when modecol9='TAXE' then vts.vts_grille9 else 0 END + case  when modecol10='TAXE' then vts.vts_grille10 else 0 END
,Discount=100*( case when modecol4='REMISE' then vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then vts.vts_grille10 else 0 END)
,Commission= 100*(case when modecol4='COMMISSION' then vts.vts_grille4 else 0 END + case  when modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='COMMISSION' then vts.vts_grille10 else 0 END)
,TotalTax= 100*(case  when modecol4='REMISE'  then - vts.vts_grille4 when modecol4='TAXE' or modecol4='COMMISSION' then  vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then - vts.vts_grille5 when modecol5='TAXE' or modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' or modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' or modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE' or modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE'or modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' or modecol10='COMMISSION' then  vts.vts_grille10 else 0 END	)
,TotalAmount= 100*(vts.vts_grille1+vts.vts_grille2+case when modecol4='REMISE' then - vts.vts_grille4  when modecol4='TAXE' or modecol4='COMMISSION' then vts.vts_grille4 else 0 END + case  when modecol5='REMISE' then - vts.vts_grille5 when modecol5='TAXE' or modecol5='COMMISSION' then vts.vts_grille5 else 0 END + case  when modecol6='REMISE' then - vts.vts_grille6 when modecol6='TAXE' or modecol6='COMMISSION' then vts.vts_grille6 else 0 END + case  when modecol7='REMISE' then - vts.vts_grille7 when modecol7='TAXE' or modecol7='COMMISSION' then vts.vts_grille7 else 0 END + case  when modecol8='REMISE' then - vts.vts_grille8 when modecol8='TAXE' or modecol8='COMMISSION' then vts.vts_grille8 else 0 END + case  when modecol9='REMISE' then - vts.vts_grille9 when modecol9='TAXE'or modecol9='COMMISSION' then vts.vts_grille9 else 0 END + case  when modecol10='REMISE' then - vts.vts_grille10 when modecol10='TAXE' or modecol10='COMMISSION' then vts.vts_grille10 else 0 END	 )
,TicketAmount=100 *vts.vts_grille3, seance_date_deb as sessionStartDate, seance_date_fin as sessionEndDate,
 case s.NEPASAFFICHERDATE 
 when 'O' then 0
 when 'N' then 1
 when '' then 1
 when null then 1
 END  as afficherLaDate
FROM valeur_tarif_stock[eventID] vts
INNER JOIN structure st ON st.structure_id >0

INNER JOIN categorie cat ON cat.categ_id = vts.categ_id
LEFT JOIN traduction_categorie tcat ON tcat.categ_id = vts.categ_id and tcat.langue_id = @langId

INNER JOIN type_tarif tt ON tt.type_tarif_id = vts.type_tarif_id
LEFT JOIN traduction_type_tarif ttt ON  ttt.type_tarif_id = vts.type_tarif_id and ttt.langue_id = @langId

inner join filieres_droits fd ON fd.type_tarif_id = tt.type_tarif_id 
INNER JOIN operateur_droit od ON od.droit_table_id = tt.type_tarif_id 

INNER JOIN seance s ON s.seance_Id = vts.seance_id
INNER JOIN @mysessions ms ON ms.seance_id = s.seance_Id

INNER JOIN entree_[eventID] e ON e.seance_id =ms.seance_id and e.categorie_id = cat.categ_id
INNER JOIN reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id 

INNER JOIN zone z on z.zone_id = rlp.zone_id
LEFT JOIN traduction_zone tzone on tzone.zone_id = z.zone_id and tzone.langue_id = @langId

INNER JOIN etage et on et.etage_id = rlp.etage_id
LEFT JOIN traduction_etage tetage on tetage.etage_id = et.etage_id and tetage.langue_id = @langId

INNER JOIN section sect on sect.section_id = rlp.section_id
LEFT JOIN traduction_section tsection on tsection.section_id = sect.section_id and tsection.langue_id = @langId

WHERE fd.filiere_id in ({listfilieresId}) and valeur='O'
AND od.operateur_id in ({listoperateursId}) AND od.droit_valeur =1 and droit_table ='TYPE_TARIF'

AND vts_v =(	SELECT MAX(vts_v) FROM valeur_tarif_stock[eventID] vts2
					WHERE vts2.tarif_logique_id=vts.tarif_logique_id
					and vts2.seance_id=vts.seance_id
					and vts2.categ_id= vts.categ_id
					and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0

AND e.entree_etat<>'X'

	--ORDER BY vts.seance_id, cat.pref_affichage, cat.categ_nom, cat.categ_id, tt.pref_affichage, tt.type_tarif_id
) gt
GROUP BY 
 sessionid, categoryid, catdisplayOrder, priceid, sessionid, categname, pricename, zoneId, floorId, sectionid, zoneName, floorName, sectionName, RailingPriceID, AmountExceptTax, Charge,tax,Discount, Commission,
TotalTax, TotalAmount, TicketAmount, sessionStartDate, sessionEndDate, tarifdisplayOrder, gt.afficherLaDate
ORDER BY sessionid, zoneid, floorId, sectionId,  catdisplayOrder, categName, tarifdisplayOrder, priceName