/* getGpsByIds.sql */



DECLARE @const_placementlibre int; set @const_placementlibre =32;
DECLARE @const_priseauto int; set @const_priseauto =16;
DECLARE @const_vueplacement int; set @const_vueplacement =8;
DECLARE @const_choixsurplan int; set @const_choixsurplan =1;

CREATE TABLE #tableresultgrilletarif 
( 
			gestion_place_id  int,
			event_Id		 INT, 
			session_id       INT, 
			vts_id			INT, 
			amountexcepttax DECIMAL(18, 10), 
			charge          DECIMAL(18, 10), 
			tax             DECIMAL(18, 10), 
			discount        DECIMAL(18, 10), 
			commission      DECIMAL(18, 10), 
			totaltax        DECIMAL(18, 10), 
			totalamount     DECIMAL(18, 10), 
			ticketamount    DECIMAL(18, 10)         
); 

--select *from gestion_place 

DECLARE @myGps AS intsList
insert into @myGps select gestion_place_id from gestion_place gp where gestion_place_id in ({gpids})

DECLARE @manifId INT
DECLARE eventcursor CURSOR SCROLL FOR 
	select distinct manif_id from gestion_place gp where gestion_place_id in ({gpids})

OPEN eventcursor 
FETCH next FROM eventcursor INTO @manifId 
WHILE @@FETCH_STATUS = 0 
BEGIN 
	declare @sql nvarchar(max)
	set @sql = '
	SELECT gp.gestion_place_id, ' + CONVERT(varchar, @manifId) + ' as manif_id, vts.seance_id, vts_id, AmountExceptTax = vts_grille1, 	
	Charge = vts_grille2
	,Tax= case when modecol4=''TAXE'' then vts.vts_grille4 else 0 END + case  when modecol5=''TAXE'' then vts.vts_grille5 else 0 END + case  when modecol6=''TAXE'' then vts.vts_grille6 else 0 END + case  when modecol7=''TAXE'' then vts.vts_grille7 else 0 END + case  when modecol8=''TAXE'' then vts.vts_grille8 else 0 END + case  when modecol9=''TAXE'' then vts.vts_grille9 else 0 END + case  when modecol10=''TAXE'' then vts.vts_grille10 else 0 END
	,Discount= case when modecol4=''REMISE'' then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then vts.vts_grille10 else 0 END
	,Commission= case when modecol4=''COMMISSION'' then vts.vts_grille4 else 0 END + case  when modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''COMMISSION'' then vts.vts_grille10 else 0 END 	
	,TotalTax= case  when modecol4=''REMISE''  then - vts.vts_grille4 when modecol4=''TAXE'' or modecol4=''COMMISSION'' then  vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' or modecol5=''COMMISSION'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' or modecol6=''COMMISSION'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' or modecol7=''COMMISSION'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' or modecol8=''COMMISSION'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE''or modecol9=''COMMISSION'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' or modecol10=''COMMISSION'' then  vts.vts_grille10 else 0 END
	,TotalAmount= vts.vts_grille1+vts.vts_grille2+case when modecol4=''REMISE'' then - vts.vts_grille4  when modecol4=''TAXE''  then vts.vts_grille4 else 0 END + case  when modecol5=''REMISE'' then - vts.vts_grille5 when modecol5=''TAXE'' then vts.vts_grille5 else 0 END + case  when modecol6=''REMISE'' then - vts.vts_grille6 when modecol6=''TAXE'' then vts.vts_grille6 else 0 END + case  when modecol7=''REMISE'' then - vts.vts_grille7 when modecol7=''TAXE'' then vts.vts_grille7 else 0 END + case  when modecol8=''REMISE'' then - vts.vts_grille8 when modecol8=''TAXE'' then vts.vts_grille8 else 0 END + case  when modecol9=''REMISE'' then - vts.vts_grille9 when modecol9=''TAXE'' then vts.vts_grille9 else 0 END + case  when modecol10=''REMISE'' then - vts.vts_grille10 when modecol10=''TAXE'' then vts.vts_grille10 else 0 END  
	,TicketAmount=vts.vts_grille3
	FROM valeur_tarif_stock' + CONVERT(varchar, @manifId) + ' vts 
	INNER JOIN gestion_place gp on vts.categ_id = gp.categ_id and vts.type_tarif_id = gp.type_tarif_id and gp.seance_id = vts.seance_id
	INNER JOIN @pmyGpsin mygp on gp.gestion_place_id = mygp.item
	INNER JOIN structure s on 1=1
	WHERE vts_v = (
		SELECT MAX(vts_v) from valeur_tarif_stock' + CONVERT(varchar, @manifId) + ' vts2  
		WHERE vts2.tarif_logique_id=vts.tarif_logique_id
		AND vts2.seance_id=vts.seance_id
		AND vts2.categ_id= vts.categ_id
		AND vts2.type_tarif_id= vts.type_tarif_id) 
	AND vts_grille1 >=0'
	print @sql
	insert into #tableresultgrilletarif
		exec sp_executesql @sql, N'@pmyGpsin intsList readonly', @myGps
	FETCH next FROM eventcursor INTO @manifId 
END 

CLOSE eventcursor 
DEALLOCATE eventcursor 
	
--select * from #tableresultgrilletarif



SELECT 
convert(int,gp.gestion_place_id) as gestion_place_id,
manif_id,
convert(int, seance_id) as seance_id,
gp.categ_id,
gp.type_tarif_id,
convert(int, gtarif.totalamount * 100) as totalamount,
convert(int, gtarif.Charge * 100) as Charge,
convert(int, gtarif.ticketamount * 100) as ticketamount,
formule_id,
nb_max,
nb_min,
iscontrainteidentite,
dispo,
isvalide,
gestion_place_parent_id,
convert(bit,gp.aucune_reserve) as aucune_reserve,
gpr.reserve_id,
r.reserve_nom as reserve_name,
r.reserve_code,
tt.type_tarif_nom,
cat.categ_nom,
o.offre_id, o.offre_nom,
adh.Adhesion_Catalog_ID,
adh.Catalog_Libelle,
gtarif.vts_id,
vueplacement = CASE WHEN (gp.prise_place & @const_vueplacement)=0 THEN 0 ELSE 1 END
FROM gestion_place gp
LEFT JOIN #tableresultgrilletarif gtarif on gtarif.gestion_place_id = gp.gestion_place_id 
LEFT JOIN type_tarif tt on tt.type_tarif_id = gp.type_tarif_id
LEFT JOIN categorie cat on cat.categ_id = gp.categ_id
LEFT OUTER JOIN gestion_place_reserve gpr on gpr.gestion_place_id = gp.gestion_place_id
LEFT OUTER JOIN reserve r on gpr.reserve_id = r.reserve_id
LEFT JOIN offre_gestion_place ogp on ogp.gestion_place_id = gp.gestion_place_id
LEFT JOIN offre o on o.offre_id = ogp.offre_id
LEFT JOIN adhesion_catalog_offresliees adhoffre on adhoffre.offre_id = o.offre_id
LEFT JOIN adhesion_catalog adh on adhoffre.adhesion_catalog_id = adh.Adhesion_Catalog_ID

WHERE gp.gestion_place_id in ({gpids})


DROP TABLE #tableresultgrilletarif




