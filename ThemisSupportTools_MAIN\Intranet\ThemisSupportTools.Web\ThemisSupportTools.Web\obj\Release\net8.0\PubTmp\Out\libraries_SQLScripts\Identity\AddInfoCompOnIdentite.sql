
/*
declare @pIdentiteId int 
declare @pInfoCompId int
declare @pInfocompValeur1 varchar(max) = ''
declare @pInfocompValeur2 varchar(max) = ''
declare @pInfocompValeur3 varchar(max) = ''
declare @pInfocompValeur4 varchar(max) = ''
declare @pIsSupprimer varchar(2) = ''
*/



INSERT INTO identite_infos_comp (identite_id,info_comp_id,valeur1,valeur2,valeur3,valeur4,supprimer,datecreation,datemodification) 
SELECT @pIdentiteId, @pInfoCompId, ISNULL(@pInfocompValeur1, ''), ISNULL(@pInfocompValeur2, ''), ISNULL(@pInfocompValeur3,''), ISNULL(@pInfocompValeur4,''), @pIsSupprimer,getdate(),getdate()
where 0 = (select  COUNT(*)  from identite_infos_comp where identite_id=@pIdentiteId AND info_comp_id = @pInfoCompId AND supprimer = 'N' ) 


