﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.webServer>
			<handlers>
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			</handlers>
			<aspNetCore processPath="dotnet" arguments=".\ThemisSupportTools.Web.dll" stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" >
				<environmentVariables>
					<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Development" />
				</environmentVariables>
			</aspNetCore>
		</system.webServer>
	</location>
</configuration>
<!--ProjectGuid: dcd2c40a-9cf6-4b62-9fee-7c82650b4b68-->