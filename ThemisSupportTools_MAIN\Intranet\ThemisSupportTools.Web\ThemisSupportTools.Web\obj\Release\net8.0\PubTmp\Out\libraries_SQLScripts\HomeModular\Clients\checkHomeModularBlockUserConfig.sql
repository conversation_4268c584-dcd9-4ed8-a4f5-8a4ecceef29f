

--declare @pCheckForAdmin int = 0

IF (NOT EXISTS (SELECT * 
                 FROM INFORMATION_SCHEMA.TABLES 
                 WHERE   TABLE_NAME = 'HomeModular_BlockUserConfig'))
BEGIN
	select 'KO:TABLENOTFOUND' as result
END
ELSE
BEGIN
	declare @nbConfig int = 0
	if @pCheckForAdmin > 0
		BEGIN
			set @nbConfig = (select count(*) from HomeModular_EmplacementGroup )
	
		END
	ELSE
		BEGIN
			set @nbConfig = (select count(*) from HomeModular_BlockEmplacement)
		END


		
	if @nbConfig > 0
		BEGIN
			select 'OK:CONFIGSOK' as result
		END
	ELSE
	BEGIN
		select 'KO:NOTCONFIG' as result
	END

END



