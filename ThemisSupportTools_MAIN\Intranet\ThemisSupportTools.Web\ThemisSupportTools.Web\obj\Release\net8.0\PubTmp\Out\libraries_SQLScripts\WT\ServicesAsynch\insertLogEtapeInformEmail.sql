﻿


DECLARE @etpId int 
	SELECT @etpId = etape_id FROM logs_etapes_creationCmds_reference r WHERE r.code='EMAILINFORM'
IF EXISTS(SELECT 1 FROM sys.columns 
          WHERE Name = N'action_id'
          AND Object_ID = Object_ID(N'logs_etapes_informEmail'))
BEGIN
	INSERT INTO logs_etapes_informEmail (panier_id,action_id, etape_id, etat) VALUES (@basketId,@actionid, @etpId,@boolEtapeDF)
END
ELSE
BEGIN

	INSERT INTO logs_etapes_informEmail (panier_id, etape_id, etat) VALUES (@basketId,@etpId,@boolEtapeDF)
END