/* checkAdhesionToConsumerProduct.sql.sql */
/*

declare @pIdentiteId int 
set @pIdentiteId = 47

declare @padherentId varchar(50) 
set @padherentId = '10991161270'

declare @pPriceId int
set @pPriceId = 5650025

declare @pProdId int

*/


DECLARE @myidentite_id int
if (@padherentId != '0')
begin /* num adherent renseigné, on le check et on remplit @myidentite_id s'il est ok */
	declare @reversidentite_id int
	set @reversidentite_id = dbo.F_adhesion_num_reverse(@padherentId)
	if (@reversidentite_id is not null)
	BEGIN 
		set @myidentite_id = @reversidentite_id
	END 
	ELSE	
	BEGIN
		select 'KO:CANTRETRIEVEIDENTITYID' as ret
		--THROW 51000, 'cannot retrieve identite_id by this adhesion num !', 1;  
	END
END
ELSE
BEGIN
	set @myidentite_id = @pIdentiteId;
END


IF (@myidentite_id is not null and @myidentite_id>0)
BEGIN
--check si l'identite id est déjà adherent sur la formule pointée par le gestion_place_id
	
	declare @formuleAdhesionOfGp int

	declare @nbrThisAdhesionActive int

	select @formuleAdhesionOfGp = ac.adhesion_catalog_id 
	from Adhesion_Catalog ac where produit_id = @pProdId

	

	--select @formuleAdhesionOfGp

	select @nbrThisAdhesionActive = COUNT(*) from Adhesion_Adherent_Catalog link_adh_cat 
	INNER JOIN Adhesion_Adherent aa ON aa.Adhesion_Adherent_ID = link_adh_cat.Adhesion_Adherent_ID
	where link_adh_cat.Adhesion_Catalog_ID = @formuleAdhesionOfGp and link_adh_cat.Actif = 1 and link_adh_cat.Adhesion_DateFin > GETDATE()  
	and aa.Identite_id = @myidentite_id

	if (@nbrThisAdhesionActive = 0)
	begin
			-- /////// periode d'achat ???
			declare @catalogAchatPossible int
			select @catalogAchatPossible = COUNT(*) from Adhesion_Catalog_Propriete acpp WHERE acpp.Adhesion_Catalog_ID = @formuleAdhesionOfGp
			and Propriete_Code='PERIODE_ACHAT' and Propriete_Valeur_Date1 < GETDATE() and Propriete_Valeur_Date2 > GETDATE()
			if (@catalogAchatPossible>0)
			begin
				select 'OK:NEWADHESION' as ret
			end
			else
			begin
				select 'KO:ACHATIMPOSSIBLE' as ret
			end
	end
	else

	begin
		-- ////////////// j'ai déja cette adhesion
			select 'KO:ALREADYCONSUMED' as ret

	end
	


END
ELSE
BEGIN
	select 'KO:CANTRETRIEVEIDENTITYID' as ret
	--THROW 51000, 'myidentite_id not found !', 1;  
END


/* cas :

- machin est déjà adherent de la formule/catalog et n'a pas encore pris cette séance (avec ce tarif) =====> ok, pas de rajout de carte à faire (remonter la carte active avec les infos)

- machin est déjà adherent de la formule/catalog a déjà pris cette séance (ou manif!) (avec ce tarif) =====> pas possible ! (remonter la carte active avec les infos)

- machin n'est pas adherent 
	- formule encore en vente ====> ok, il faudra ajouter la carte 
	
	- formule n'est plus en vente ===> pas possible !
errorcode = 0 

si deja adhérent
	offer == null
pas adherent
	offer = offer
	
	identite ???

déja adhérent et déja pris ce tarif (séance ou manif) selong prostock
	error code = 409 
	offer = null

pas adhérent mais est plus en vente (plus dans période d'achat)
	error code = 404
	offer = null


déja adhérent mais la carte est arrivée a échéance 



*/


