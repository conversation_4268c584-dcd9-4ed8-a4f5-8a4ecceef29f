
/* */
DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

SELECT distinct z.zone_id as zone_id,
CASE WHEN zt.zone_nom is null then z.zone_nom else zt.zone_nom end as zone_name
,z.zone_code, z.pref_affichage, z.zone_couleur_id 
FROM zone z 
LEFT JOIN traduction_zone zt on zt.zone_id = z.zone_id and zt.langue_id = @LgId
WHERE z.zone_id in ({zonesids})
