@using Core.Themis.Libraries.Razor.Common.ViewModels.Modals

@model ConfirmationModalSettings

<div class="widget-modal fade @Model.CustomModalCssClass" 
     id="@Model.Id" 
     data-bs-backdrop="static" 
     data-bs-keyboard="false" 
     tabindex="-1" 
     aria-labelledby="@Model.Id-Label" 
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="@Model.Id-Label">@Model.Title</h1>
                <button type="button" 
                        class="btn-close"
                        aria-label="Close" 
                        onclick="sendConfirmationModalResponse('@Model.IframeSelector','@Model.Id', false)">
                </button>
            </div>
            <div class="modal-body">
                @Html.Raw(Model.BodyContent)
            </div>
            <div class="modal-footer">
                <button type="button" 
                        class="btn @Model.CancelButtonCssClass"
                        onclick="sendConfirmationModalResponse('@Model.IframeSelector','@Model.Id', false)">
                    @Model.CancelButtonLabel
                </button>
                <button type="button" 
                        class="btn @Model.ConfirmButtonCssClass"
                        onclick="sendConfirmationModalResponse('@Model.IframeSelector','@Model.Id', true)">
                    @Model.ConfirmButtonLabel
                </button>
            </div>
        </div>
    </div>
</div>