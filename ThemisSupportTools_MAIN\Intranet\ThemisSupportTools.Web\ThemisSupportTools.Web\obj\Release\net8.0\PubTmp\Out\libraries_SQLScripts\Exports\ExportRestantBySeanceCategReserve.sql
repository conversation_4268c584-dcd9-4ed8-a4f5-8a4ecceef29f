﻿/*
declare @pPageLength int = 5
declare @pPage int = 2
*/

declare @ManifId int

declare @myplaceId int =0
set @myplaceId = @pplaceId


declare @myeventId int =0
set @myeventId = @peventid

declare @myseanceid int = 0
set @myseanceid = @psessionid

if (@myeventId = 0 and @myseanceid>0)
begin
	select @myeventId = manifestation_id from seance s where s.seance_Id = @myseanceid
end


DECLARE @PageNumber AS INT
DECLARE @RowsOfPage AS INT

SET @PageNumber=@ppage
SET @RowsOfPage=@ppagelength

declare @totalEventsCount int = 0

SELECT @totalEventsCount = count(distinct[manifestation_id]) /* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
FROM [seance]
WHERE seance_date_fin > getdate()
AND (seance_date_deb<dateadd(year, 10, getdate())
OR seance_date_deb > Convert(datetime, '2099-01-01' )) -- date libre
AND seance_verrouiller = 'N'

declare @totalPagesCount int = 1
set @totalPagesCount = (@totalEventsCount-1) / @RowsOfPage + 1



DECLARE cursorManifs CURSOR FOR

SELECT distinct[manifestation_id] /* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
FROM [seance]
WHERE seance_date_fin > getdate()
AND (seance_date_deb<dateadd(year, 10, getdate())
OR seance_date_deb > Convert(datetime, '2099-01-01' )) -- date libre
AND seance_verrouiller = 'N'
AND (@myeventId = 0 or manifestation_id = @myeventId)
ORDER BY manifestation_id asc
OFFSET (@PageNumber-1)*@RowsOfPage ROWS
FETCH NEXT @RowsOfPage ROWS ONLY

DECLARE @resultsFrees as TABLE (event_id int, event_name varchar(50), place_name varchar(50), place_id int, seatsFrees_count int, session_id int, session_start_date datetime,
reserve_id int, reserve_name varchar(100), categ_id int, categ_name varchar(50))

DECLARE @resultsAlotissement as TABLE (event_id int, event_name varchar(50), place_name varchar(50), place_id int, seatsAlotissement_count int, session_id int, session_start_date datetime,
reserve_id int, reserve_name varchar(100), categ_id int, categ_name varchar(50))

DECLARE @resultsTotal as TABLE (pageNumber int, pagesCount int, totalElementsCount int, event_id int, event_name varchar(50), place_name varchar(50), place_id int, seatsFrees_count int, seatsAlotissement_count int, seatsTotal_count int, session_id int, session_start_date datetime,
reserve_id int, reserve_name varchar(100), categ_id int, categ_name varchar(50))

OPEN cursorManifs;

FETCH NEXT FROM cursorManifs INTO @MANIFID;

while(@@FETCH_STATUS=0)

BEGIN

	declare @sql varchar(max)

	set @sql = 'SELECT s.manifestation_id, m.manifestation_nom, l.lieu_nom, l.lieu_id, COUNT(entree_id) as seatsFreesCount, s.seance_id, s.seance_date_deb,
	e.reserve_id,
	case when e.reserve_id = 0 then ''none'' else res.reserve_nom end AS reserve_nom,
	e.categorie_id, cat.categ_nom FROM entree_' + CONVERT(varchar, @manifId) + ' e
	INNER JOIN categorie cat on cat.categ_id = e.categorie_id
	LEFT JOIN reserve res on res.reserve_id = e.reserve_id
	INNER JOIN seance s on s.seance_Id = e.seance_id
	INNER JOIN lieu l on s.lieu_id = l.lieu_id
	INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
	WHERE s.seance_date_deb > GETDATE() and e.entree_etat =''L'' '

	IF (@myseanceid <> 0)
		set @sql = @sql + ' and s.seance_id = ' + CONVERT(varchar, @myseanceid); 

	IF (@myplaceId <> 0)
		set @sql = @sql + ' and s.lieu_id = ' + CONVERT(varchar, @myplaceId); 

	SET @sql = @sql + ' GROUP BY s.seance_id, s.seance_date_deb, e.reserve_id, categorie_id, cat.categ_nom , cat.pref_affichage,
	res.reserve_nom, s.manifestation_id, m.manifestation_nom, l.lieu_nom, l.lieu_id
	order by s.seance_date_deb, cat.pref_affichage, e.reserve_id'

	print @SQL

	INSERT INTo @resultsFrees (event_id, event_name, place_name, place_id, seatsFrees_count, session_id, session_start_date, reserve_id, reserve_name, categ_id, categ_name)
	EXEC(@SQL)

		set @sql = 'SELECT s.manifestation_id, m.manifestation_nom, l.lieu_nom, l.lieu_id, COUNT(entree_id) as seatsContingent_count, s.seance_id, s.seance_date_deb,
	e.reserve_id,
	case when e.reserve_id = 0 then ''none'' else res.reserve_nom end AS reserve_nom,
	e.categorie_id, cat.categ_nom FROM entree_' + CONVERT(varchar, @manifId) + ' e
	inner join categorie cat on cat.categ_id = e.categorie_id
	LEFT JOIN reserve res on res.reserve_id = e.reserve_id
	INNER JOIN seance s on s.seance_Id = e.seance_id
	INNER JOIN lieu l on s.lieu_id = l.lieu_id
	INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
	WHERE s.seance_date_deb > GETDATE() and e.alotissement_id <>0 '

	IF (@myseanceid <> 0)
		set @sql = @sql + ' and s.seance_id = ' + CONVERT(varchar, @myseanceid); 

	IF (@myplaceId <> 0)
		set @sql = @sql + ' and s.lieu_id = ' + CONVERT(varchar, @myplaceId); 

	set @sql = @sql + ' GROUP BY s.seance_id, s.seance_date_deb, e.reserve_id, categorie_id, cat.categ_nom , cat.pref_affichage,
	res.reserve_nom, s.manifestation_id, m.manifestation_nom, l.lieu_nom, l.lieu_id
	order by s.seance_date_deb, cat.pref_affichage, e.reserve_id'

	print @SQL

	INSERT INTo @resultsAlotissement (event_id, event_name, place_name, place_id, seatsAlotissement_count, session_id, session_start_date, reserve_id, reserve_name, categ_id, categ_name)
	EXEC(@SQL)


	SET @sql = 'SELECT s.manifestation_id, m.manifestation_nom, l.lieu_nom, l.lieu_id, 0 as seatsFreesCount, 0 as seatsAlotissement_count, COUNT(entree_id) as seatsTotalCount, s.seance_id, s.seance_date_deb,
	e.reserve_id,
	case when e.reserve_id = 0 then ''none'' else res.reserve_nom end AS reserve_nom,
	e.categorie_id, cat.categ_nom FROM entree_' + CONVERT(varchar, @manifId) + ' e
	INNER JOIN categorie cat on cat.categ_id = e.categorie_id
	LEFT JOIN reserve res on res.reserve_id = e.reserve_id
	INNER JOIN seance s on s.seance_Id = e.seance_id
	INNER JOIN lieu l on s.lieu_id = l.lieu_id
	INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
	WHERE s.seance_date_deb > GETDATE() and entree_etat<>''X''  '

	IF (@myseanceid <> 0)
		set @sql = @sql + ' and s.seance_id = ' + CONVERT(varchar, @myseanceid); 

	IF (@myplaceId <> 0)
		set @sql = @sql + ' and s.lieu_id = ' + CONVERT(varchar, @myplaceId); 

	SET @sql = @sql + ' GROUP BY s.seance_id, s.seance_date_deb, e.reserve_id, categorie_id, cat.categ_nom , cat.pref_affichage,
	res.reserve_nom, s.manifestation_id, m.manifestation_nom, l.lieu_nom, l.lieu_id
	order by s.seance_date_deb, cat.pref_affichage, e.reserve_id'

	print @SQL

	INSERT INTO @resultsTotal (event_id, event_name, place_name, place_id, seatsFrees_count, seatsAlotissement_count, seatsTotal_count, session_id, session_start_date, reserve_id, reserve_name, categ_id, categ_name)
	EXEC(@SQL)

FETCH NEXT FROM cursorManifs INTO @MANIFID;

END

CLOSE cursorManifs;
DEALLOCATE cursorManifs;

--select * from @resultsFrees

--select * from @resultsTotal
UPDATE @resultsTotal set pageNumber = @PageNumber, pagesCount = @totalPagesCount, totalElementsCount = @totalEventsCount

UPDATE t set t.seatsFrees_count = f.seatsFrees_count FROM @resultsTotal t
INNER JOIN @resultsFrees f on f.session_id = t.session_id and f.categ_id = t.categ_id and f.reserve_id = t.reserve_id and f.event_id = t.event_id

UPDATE t set t.seatsAlotissement_count = f.seatsAlotissement_count FROM @resultsTotal t
INNER JOIN @resultsAlotissement f on f.session_id = t.session_id and f.categ_id = t.categ_id and f.reserve_id = t.reserve_id and f.event_id = t.event_id

SELECT * FROM @resultsTotal