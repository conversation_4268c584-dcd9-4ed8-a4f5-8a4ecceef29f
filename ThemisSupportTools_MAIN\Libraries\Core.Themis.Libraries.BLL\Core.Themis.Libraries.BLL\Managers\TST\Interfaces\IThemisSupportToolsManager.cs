﻿using Core.Themis.Libraries.BLL.Services.Access.Models;
using Core.Themis.Libraries.Data.Entities.WsAdmin.ThemisSupportTools;
using Core.Themis.Libraries.DTO.CouponsPromo;
using Core.Themis.Libraries.DTO.LieuPhysique;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.ProfiteurAcheteur;
using Core.Themis.Libraries.DTO.TST;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL.Managers.TST.Interfaces;

public interface IThemisSupportToolsManager
{
    /// <summary>
    /// Check les droits de l'utilisateur connecté
    /// </summary>
    /// <param name="claims"></param>
    /// <returns></returns>
   Task<bool> CheckRightsOfUser(ClaimsPrincipal user, AuthorizationPolicy policy);

       // List<TstActiveDirectoryGroupDTO>? GetActiveDirectoryGroupWithDependanciesByExternalIds(List<string> activeDirectoryGroupExternalIds);

    Task<List<TstActiveDirectoryGroupDTO>?> GetActiveDirectoryGroupWithDependanciesByExternalIdsAsync(List<string> activeDirectoryGroupExternalIds);
    TstRoleDTO? GetTstAccessByModuleIdAndActiveDirectoryGroupId(int moduleId, List<int> activeDirectoryGroupIds);

    List<TstAccessModel> GetTstAccessByActiveDirectoryGroupsId(string[] activeDirectoryGroupIds);

    Task<TstConfigIniViewModel> GetConfigIniAsync(int structureId, string currentUser);

    void SetSelectDatas(List<ConfigIniSectionDTO> configIniSections);

    List<SelectLookup> GetSectionGroupe(List<ConfigIniSectionDTO> configIniSections, string sectionGroupeName);

    bool DeleteConfigIniTempFile(int structureId, string userName);
    Task SauvegarderConfigIniAsync(int idStructure,  TstConfigIniViewModel tstConfigIniViewModel);
    List<TstModuleDTO> GetModulesAsync();
    List<TstRoleDTO>? GetRolesAsync();
    Task<List<TstActiveDirectoryGroupDTO>> GetAdGroupsAsync();
    Task AssignRoleToAdGroupAsync(int[] selectedAdGroupIds, int[] selectedModuleIds, int[] selectedRoleIds);
    Task CreateRoleAsync(TstRoleDTO role);
    Task CreateModuleAsync(TstModuleDTO module);
    Task CreateDirectoryGroupAsync(TstActiveDirectoryGroupDTO directorygroupe);
    Task<List<TstAccessModel>> GetAllAccessesAsync();
    TstRoleEntity? GetRoleByIdAsync(int roleId);
    TstModuleEntity? GetTstModuleById(int moduleId);
    TstActiveDirectoryGroupEntity? GetActiveDirectoryGroupById(int activeDirectoryGroupId);

    Task<bool> UpdateParamsDeflagAsync(int structureId, int delaiDeflag);
    Task<int?> GetParamsDeflagAsync(int structureId);
    Task<(int tempsExpiration, int delaiDeflag)> ChargerConfigurationTempsPanierAsync(int structureId, string userName, int? deflagFromDb);
    Task<bool> EnregistrerConfigurationTempsPanierAsync(int structureId, string userName, int tempsExpiration, int delaiDeflag);
    Task<IEnumerable<ProfilAcheteurDTO>> GetBuyerProfiles(int structureId);
    Task<List<LieuPhysiqueDTO>> GetNonMaskedLieuxPhysiquesAsync(int structureId);
    Task<bool> TransferPhotoPointageAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId);
    Task<List<int>> GetLieuxPhysiquesWithImagesAsync(int structureId);
    Task ExportCustomReferenceSalleImageXmlAsync(int structureId, string environment);
    Task ExportCustomReferenceSalleImageXmlByLieuPhysiqueAsync(int structureId, int lieuPhysiqueId, string environment);
    Task<bool> TransfereVignetteAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId);
    Task<int> GenerateCouponsAsync(int structureId, int numberOfCoupons, string template, string prefixe, int profilAcheteurId, DateTime dateDebValidite, DateTime dateFinValidite);
    Task<(IEnumerable<dynamic> SourceOnly, IEnumerable<dynamic> DestinationOnly)> CompareLieuxPhysiquesAsync(int structureId, int sourceLieuPhysiqueId, int destinationLieuPhysiqueId);
    Task ExportCustomReferenceWebXmlAsync(int structureId, string environment);
    Task ExportCustomReferenceWebXmlByLieuPhysiqueAsync(int structureId, int lieuPhysiqueId, string environment);
    Task UpdateModuleAsync(TstModuleDTO module);

    Task<bool> DeleteModuleByIdAsync(int moduleId);

}
