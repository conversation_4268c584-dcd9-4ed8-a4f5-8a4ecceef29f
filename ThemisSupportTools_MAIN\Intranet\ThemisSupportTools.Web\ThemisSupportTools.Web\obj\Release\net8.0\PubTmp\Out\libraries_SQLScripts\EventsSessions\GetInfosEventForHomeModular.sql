/*
declare @pLangCode varchar(max) = 'FR'
declare @pSessionId int = 1

*/

declare @langueId int = (select langue_id from langue where langue_code = @pLangCode)



select  m.manifestation_nom,tm.manifestation_nom as trad_manifestation_nom, s.seance_date_deb from manifestation m 
inner join seance s on s.manifestation_id = m.manifestation_id
left outer join traduction_manifestation tm on tm.manifestation_id = m.manifestation_id and tm.langue_id = @langueId
where m.supprimer = 'N' and s.supprimer ='N' and s.seance_verrouiller='N'
and s.seance_id = @pSessionId


