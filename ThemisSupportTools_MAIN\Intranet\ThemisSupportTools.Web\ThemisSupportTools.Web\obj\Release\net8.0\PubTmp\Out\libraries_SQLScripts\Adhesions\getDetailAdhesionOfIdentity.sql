

/*


declare @pAdhesionCatalogId int
set @pAdhesionCatalogId = 10


*/


SELECT tt.type_tarif_id 
   , tt.type_tarif_nom 
   , acp.Propriete_Valeur_int2
   , acp.Propriete_Valeur_int3
   , acp.propriete_code
FROM Adhesion_Catalog AS ac
INNER JOIN Adhesion_Catalog_Type_Tarif AS actt 
   ON actt.Adhesion_Catalog_id = ac.Adhesion_Catalog_id
INNER JOIN Adhesion_Catalog_Propriete AS acp 
   ON acp.Adhesion_Catalog_id = ac.Adhesion_Catalog_id
INNER JOIN type_tarif AS tt 
   ON tt.type_tarif_id = acp.propriete_valeur_int1
       AND acp.propriete_valeur_int1 = actt.type_tarif_id
       AND (acp.propriete_code = 'TARIF_MAITRE' 
            OR acp.propriete_code = 'TARIF_ELEVE') 
WHERE ac.Adhesion_Catalog_id = @pAdhesionCatalogId 