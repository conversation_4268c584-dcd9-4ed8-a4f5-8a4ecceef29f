
/*declare @plangCode varchar(5) = 'en'*/

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0



SELECT  d.lieu_id, d.denom_id,
	case when tr.denom_nom is null then d.denom_nom else tr.denom_nom end as denom_nom,
	d.denom_code, d.denom_couleur_id
	
FROM denomination d
LEFT OUTER JOIN traduction_denomination tr on tr.denom_id = d.denom_id and tr.langue_id = @LgId

