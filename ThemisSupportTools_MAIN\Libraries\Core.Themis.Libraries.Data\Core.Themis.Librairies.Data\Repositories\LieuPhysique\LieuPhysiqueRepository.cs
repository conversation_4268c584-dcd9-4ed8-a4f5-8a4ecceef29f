
using AutoMapper;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.Entities.LieuPhysique;
using Core.Themis.Libraries.Data.Repositories.Common;
using Core.Themis.Libraries.Data.Repositories.LieuPhysique.Interface;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Dapper;
using Core.Themis.Libraries.Utilities.Helpers.Sql;
using System;
using System.Xml;
using System.Linq;
using System.Xml.Linq;

namespace Core.Themis.Libraries.Data.Repositories.LieuPhysique
{
    public class LieuPhysiqueRepository : GenericStructureRepository<LieuPhysiqueEntity>, ILieuPhysiqueRepository
    {

        private readonly IDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly string _pathForSqlScript;



        public LieuPhysiqueRepository(
            IDbContext dbContext,
            IMapper mapper,
            IMemoryCache memoryCache,
            IConfiguration config) : base(ContextType.Open, dbContext, memoryCache, mapper)
        {
            _dbContext = dbContext;
            _pathForSqlScript = config["PathForSqlScript"]!;
            _mapper = mapper;
        }


        public async Task<List<LieuPhysiqueEntity>> GetNonMaskedLieuxPhysiquesAsync(int structureId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/ListePlanSansImage", "listePlanSansImage", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryAsync<LieuPhysiqueEntity>(sqlQuery);
                return results.AsList();
            }
            catch
            {
                throw;
            }
        }

        public async Task<List<int>> GetLieuxPhysiquesWithImagesAsync(int structureId)
        {
            try 
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/ListePlanAvecImage", "listePlanAvecImage", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryAsync<int>(sqlQuery);
                return results.AsList();
            }
            catch
            {
                throw;
            }
        }


        public async Task<(IEnumerable<dynamic> SourceOnly, IEnumerable<dynamic> DestinationOnly)> CompareLieuxPhysiquesAsync(int structureId, int sourceLieuPhysiqueId, int destinationLieuPhysiqueId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/CompareLieuxPhysiques", "compareLieuxPhysiques", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryMultipleAsync(sqlQuery, new
                {
                    SourceId = sourceLieuPhysiqueId,
                    DestinationId = destinationLieuPhysiqueId
                }).ConfigureAwait(false);

                var sourceOnly = await results.ReadAsync();
                var destinationOnly = await results.ReadAsync();

                return (sourceOnly, destinationOnly);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la comparaison des lieux physiques : {ex.Message}");
                throw;
            }
        }

        public async Task<bool> TransferPhotoPointageAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/TransferePointagePhoto", "transferePointagePhoto", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                int rowsAffected = await connection.ExecuteAsync(sqlQuery, new { SourceLieuPhysiqueId = sourceLieuPhysiqueId, TargetLieuPhysiqueId = targetLieuPhysiqueId }).ConfigureAwait(false);
                return rowsAffected > 0;
            }
            catch (Exception)
            {
                throw;
            }
        }


        public async Task<bool> TransfereVignetteAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/TransfereVignette", "transfereVignetteAsync", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                int rowsAffected = await connection.ExecuteAsync(sqlQuery, new { SourceLieuPhysiqueId = sourceLieuPhysiqueId, TargetLieuPhysiqueId = targetLieuPhysiqueId }).ConfigureAwait(false);
                return rowsAffected > 0;
            }
            catch (Exception)
            {
                throw;
            }
        }


        public async Task<IEnumerable<dynamic>> GenerateReferenceSalleImageXmlWithCustomStructureAsync(int structureId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/GenerateReferenceSalleImageXml", "generateReferenceSalleImageXml", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryAsync(sqlQuery);
                return results;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la récupération des données dans le Repository : {ex.Message}");
                throw;
            }
        }

        public async Task<IEnumerable<dynamic>> GenerateReferenceWebXmlWithVignetteAsync(int structureId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/GenerateReferenceWebVignetteXml", "generateReferenceWebVignetteXml", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryAsync(sqlQuery);
                return results;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la récupération des données dans le Repository : {ex.Message}");
                throw;
            }
        }

        public async Task<IEnumerable<dynamic>> GenerateReferenceSalleImageXmlByLieuPhysiqueAsync(int structureId, int lieuPhysiqueId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/GenerateReferenceSalleImageXml", "generateReferenceSalleImageXmlByLieuPhysique", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryAsync(sqlQuery, new { lieuPhysiqueId });
                return results;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la récupération des données dans le Repository : {ex.Message}");
                throw;
            }
        }

        public async Task<IEnumerable<dynamic>> GenerateReferenceWebXmlWithVignetteByLieuPhysiqueAsync(int structureId, int lieuPhysiqueId)
        {
            try
            {
                string sqlQuery = SqlQueryHelper.GetQuery(_pathForSqlScript, "TST/GenerateReferenceWebVignetteXml", "generateReferenceWebVignetteXmlByLieuPhysique", structureId);
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                var results = await connection.QueryAsync(sqlQuery, new { lieuPhysiqueId });
                return results;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la récupération des données dans le Repository : {ex.Message}");
                throw;
            }
        }



    }
}
