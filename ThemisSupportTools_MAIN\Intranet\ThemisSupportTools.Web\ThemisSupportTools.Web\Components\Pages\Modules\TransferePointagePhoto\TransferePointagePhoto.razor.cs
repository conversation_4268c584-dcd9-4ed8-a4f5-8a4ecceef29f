﻿using Core.Themis.Libraries.Data.Entities.Open.Lieu;
using Core.Themis.Libraries.DTO.LieuPhysique;
using Core.Themis.Libraries.DTO.ReferenceLieuPhysique;
using Microsoft.AspNetCore.Components;


namespace ThemisSupportTools.Web.Components.Pages.Modules.TransferePointagePhoto
{
    public partial class TransferePointagePhoto 
    {
        private const string MODULE_NAME = "transfere-pointage-photo";

        [Inject]
        private IThemisSupportToolsManager ThemisSupportToolsManager { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; }

        [Inject]
        private ITstAccessService TstAccessService { get; set; }

        [Inject]
        private IConfiguration Configuration { get; set; }

        [Inject]
        private IStringLocalizer<Resource> Localizer { get; set; } = default!;

        [Parameter]
        public int? StructureId { get; set; }

        private List<LieuPhysiqueDTO> LieuxPhysiques { get; set; } = new();
        private List<LieuPhysiqueDTO> LieuxPhysiquesWithImages { get; set; } = new();

        private int SelectedSourceLieuPhysiqueId { get; set; }
        private int SelectedTargetLieuPhysiqueId { get; set; }

        private int SelectedSourceVignetteLieuPhysiqueId { get; set; }
        private int SelectedTargetVignetteLieuPhysiqueId { get; set; }

        private bool IsLoading { get; set; } = true;

        private string structureName = string.Empty;

        // Pour afficher les incohérences
        private List<ReferenceLieuPhysiqueDTO> IncoherencesSourceOnly { get; set; } = new();
        private List<ReferenceLieuPhysiqueDTO> IncoherencesDestinationOnly { get; set; } = new();

        private bool IncoherencesExistantes => IncoherencesSourceOnly.Count > 0 || IncoherencesDestinationOnly.Count > 0;

        private List<ToastMessage> toastMessages = new();
        private ToastsPlacement toastsPlacement = ToastsPlacement.TopRight;

        protected override async Task OnInitializedAsync()
        {
            if (StructureId is null)
            {
                NavigationManager.NavigateTo(NavigationManager.BaseUri + "choose-structure/" + MODULE_NAME);
            }
            else
            {
                if (!TstAccessService.IsGranted(MODULE_NAME))
                {
                    NavigationManager.NavigateTo("");
                    return;
                }

                var tstRole = TstAccessService.GetRoleByModuleName(MODULE_NAME);
                if (!tstRole.CanRead)
                {
                    NavigationManager.NavigateTo("");
                    return;
                }

                structureName = TstAccessService.StructureName;

                try
                {
                    await LoadLieuxPhysiques();
                }
                catch (Exception ex)
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_erreur_chargement", ex.Message], ToastType.Warning);
                }
            }
        }


        private void ChangeStructure()
        {
            NavigationManager.NavigateTo(NavigationManager.BaseUri + "choose-structure/" + MODULE_NAME);
        }

        private async Task LoadLieuxPhysiques()
        {
            
            if (!StructureId.HasValue) return;

            IsLoading = true;
            try
            {
                LieuxPhysiques = await ThemisSupportToolsManager.GetNonMaskedLieuxPhysiquesAsync(StructureId.Value);
                var lieuxWithImagesIds = await ThemisSupportToolsManager.GetLieuxPhysiquesWithImagesAsync(StructureId.Value);
                LieuxPhysiquesWithImages = LieuxPhysiques
                    .Where(l => lieuxWithImagesIds.Contains(l.LieuNomId))
                    .ToList();

                // Exclure les éléments de lieuxWithImagesIds de la liste des cibles  
                LieuxPhysiques = LieuxPhysiques
                    .Where(l => !lieuxWithImagesIds.Contains(l.LieuNomId))
                    .ToList();

                AddToastMessage(Localizer["transfert_pointagephoto_succes_chargement"], ToastType.Success);
            }
            catch (Exception ex)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_erreur_chargement", ex.Message], ToastType.Warning);
            }
            finally
            {
                IsLoading = false;
            }
        }

        // --- Méthodes de sélection ---
        private async Task OnSourceLieuPhysiqueChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out int selectedId))
            {
                SelectedSourceLieuPhysiqueId = selectedId;
            }
        }

        private async Task OnTargetLieuPhysiqueChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out int selectedId))
            {
                SelectedTargetLieuPhysiqueId = selectedId;
            }
        }

        private async Task OnSourceVignetteLieuPhysiqueChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out int selectedId))
            {
                SelectedSourceVignetteLieuPhysiqueId = selectedId;
            }
        }

        private async Task OnTargetVignetteLieuPhysiqueChanged(ChangeEventArgs e)
        {
            if (int.TryParse(e.Value?.ToString(), out int selectedId))
            {
                SelectedTargetVignetteLieuPhysiqueId = selectedId;
            }
        }

        // --- Méthode de comparaison ---
        private async Task CompareLieuxManuellement()
        {
            int sourceId = 0;
            int targetId = 0;

            if (SelectedSourceLieuPhysiqueId > 0 && SelectedTargetLieuPhysiqueId > 0)
            {
                sourceId = SelectedSourceLieuPhysiqueId;
                targetId = SelectedTargetLieuPhysiqueId;
            }
            else if (SelectedSourceVignetteLieuPhysiqueId > 0 && SelectedTargetVignetteLieuPhysiqueId > 0)
            {
                sourceId = SelectedSourceVignetteLieuPhysiqueId;
                targetId = SelectedTargetVignetteLieuPhysiqueId;
            }

            if (sourceId == 0 || targetId == 0)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_verification_selection_lieux"], ToastType.Info);
                return;
            }

            try
            {
                var result = await ThemisSupportToolsManager.CompareLieuxPhysiquesAsync(StructureId.Value, sourceId, targetId);

                IncoherencesSourceOnly.Clear();
                IncoherencesDestinationOnly.Clear();

                foreach (var item in result.SourceOnly)
                {
                    IncoherencesSourceOnly.Add(new ReferenceLieuPhysiqueDTO
                    {
                        Rang = item.rang,
                        Siege = item.siege
                    });
                }

                foreach (var item in result.DestinationOnly)
                {
                    IncoherencesDestinationOnly.Add(new ReferenceLieuPhysiqueDTO
                    {
                        Rang = item.rang,
                        Siege = item.siege
                    });
                }

                if (IncoherencesExistantes)
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_verification_incoherences_detectees"], ToastType.Warning);
                }
                else
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_verification_compatibles"], ToastType.Success);
                }

                StateHasChanged();
            }
            catch (Exception ex)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_verification_erreur"] + $" : {ex.Message}", ToastType.Warning);
            }
        }


        // --- Méthodes de transfert ---
        private async Task ProcessTransfert()
        {
            if (IncoherencesExistantes)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_transfert_incoherences_a_corriger"], ToastType.Warning);
                return;
            }

            if (SelectedSourceLieuPhysiqueId <= 0 || SelectedTargetLieuPhysiqueId <= 0)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_transfert_selection_lieux"], ToastType.Warning);
                return;
            }


            IsLoading = true;

            try
            {
                bool success = await ThemisSupportToolsManager.TransferPhotoPointageAsync(
                    StructureId.Value,
                    SelectedSourceLieuPhysiqueId,
                    SelectedTargetLieuPhysiqueId);

                if (success)
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_succes_transfert",
                        SelectedSourceLieuPhysiqueId,
                        SelectedTargetLieuPhysiqueId], ToastType.Success);

                    await LoadLieuxPhysiques(); 
                }
                else
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_echec_transfert"], ToastType.Warning);
                }
            }
            catch (Exception ex)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_exception_transfert", ex.Message], ToastType.Warning);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ProcessVignetteTransfert()
        {
            if (IncoherencesExistantes)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_transfert_incoherences_a_corriger"], ToastType.Warning);
                return;
            }

            if (SelectedSourceLieuPhysiqueId <= 0 || SelectedTargetLieuPhysiqueId <= 0)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_transfert_selection_lieux"], ToastType.Warning);
                return;
            }

            IsLoading = true;

            try
            {
                bool success = await ThemisSupportToolsManager.TransfereVignetteAsync(
                    StructureId.Value,
                    SelectedSourceVignetteLieuPhysiqueId,
                    SelectedTargetVignetteLieuPhysiqueId);

                if (success)
                {
                    AddToastMessage(Localizer["transfert_vignette_succes_transfert",
                        SelectedSourceVignetteLieuPhysiqueId,
                        SelectedTargetVignetteLieuPhysiqueId], ToastType.Success);

                    await LoadLieuxPhysiques();
                }
                else
                {
                    AddToastMessage(Localizer["transfert_vignette_echec_transfert"], ToastType.Warning);
                }
            }
            catch (Exception ex)
            {
                AddToastMessage(Localizer["transfert_vignette_exception_transfert", ex.Message], ToastType.Warning);
            }
            finally
            {
                IsLoading = false;
            }
        }

        // --- Retour / XML ---
        protected void RetourAccueil()
        {
            NavigationManager.NavigateTo("/");
        }

        private void AddToastMessage(string message, ToastType type)
        {
            toastMessages.Add(new ToastMessage { Message = message, Type = type });
        }

        private async Task GenerateCustomXmlFile()
        {
            if (!StructureId.HasValue)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_export_salle_structure_id_manquant"], ToastType.Warning);
                return;
            }

            if (SelectedSourceLieuPhysiqueId <= 0)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_export_salle_lieu_physique_manquant"], ToastType.Warning);
                return;
            }

            IsLoading = true;

            try
            {
                string environment = Configuration["ENVIRONMENT"];
                if (string.IsNullOrWhiteSpace(environment))
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_export_salle_environnement_indefini"], ToastType.Warning);
                    return;
                }

                await ThemisSupportToolsManager.ExportCustomReferenceSalleImageXmlByLieuPhysiqueAsync(StructureId.Value, SelectedSourceLieuPhysiqueId, environment);
                AddToastMessage(Localizer["transfert_pointagephoto_export_salle_xml_succes"], ToastType.Success);
            }
            catch (Exception ex)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_export_salle_xml_erreur", ex.Message], ToastType.Warning);
            }
            finally
            {
                IsLoading = false;
            }
        }


        private async Task GenerateCustomXmlFileVignette()
        {
            if (!StructureId.HasValue)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_export_structure_id_manquant"], ToastType.Warning);
                return;
            }

            if (SelectedSourceVignetteLieuPhysiqueId <= 0)
            {
                AddToastMessage(Localizer["transfert_vignette_export_lieu_physique_manquant"], ToastType.Warning);
                return;
            }

            IsLoading = true;

            try
            {
                string environment = Configuration["ENVIRONMENT"];
                if (string.IsNullOrWhiteSpace(environment))
                {
                    AddToastMessage(Localizer["transfert_pointagephoto_export_environnement_indefini"], ToastType.Warning);
                    return;
                }

                await ThemisSupportToolsManager.ExportCustomReferenceWebXmlByLieuPhysiqueAsync(StructureId.Value, SelectedSourceVignetteLieuPhysiqueId, environment);
                AddToastMessage(Localizer["transfert_pointagephoto_export_xml_succes"], ToastType.Success);
            }
            catch (Exception ex)
            {
                AddToastMessage(Localizer["transfert_pointagephoto_export_xml_erreur", ex.Message], ToastType.Warning);
            }
            finally
            {
                IsLoading = false;
            }
        }

    }


}