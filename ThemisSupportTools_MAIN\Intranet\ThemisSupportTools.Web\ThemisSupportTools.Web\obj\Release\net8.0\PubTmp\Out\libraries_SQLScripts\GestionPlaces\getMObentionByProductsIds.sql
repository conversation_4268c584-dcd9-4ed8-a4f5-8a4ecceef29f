
/* getMObentionByProductsIds.sql 
DECLARE @pIdentiteId int
DECLARE @pBuyerProfilId int 
*/


[BASKET]

select produit_id /* si au final on propose ce produit + d'autres public "tout public' on exclus ce produits */
into #prodAExclure
from produit where produit_id in ({prodJustifId})

SELECT produit_id
INTO #myprods
FROM produit
WHERE produit_id in ({prodids})

DECLARE @LgId int
SELECT @LgId = langue_id FROM langue WHERE langue_code = @plangCode

IF @LgId IS NULL
	SET @LgId = 0

DECLARE @nbrProds int /* nbr de produits */
SELECT @nbrProds = count(*) from #myprods

/* les mo pour les produits : */
SELECT 
	p.produit_id as productId,
	CASE WHEN tp.produit_nom is null then p.produit_nom else tp.produit_nom end as productName,
	CONVERT(INTEGER,(montant1 + montant2)*100) as TotalAmount ,CONVERT(INTEGER,montant2 * 100)  as Charge,
	isnull(tva.tva_libelle,'') as tva_libelle , isnull(tva.tva_taux,0) as tva_taux,
	p.pref_affichage 

FROM (
	SELECT count(*) as nbrProduitsReliees, p.produit_id 
	FROM link_produit_internet_type_envoi lkt 
	INNER JOIN #myprods mp  on mp.produit_id = lkt.produit_internet_id	
	INNER JOIN produit p on p.produit_id = lkt.produit_type_envoi_id
	GROUP BY p.produit_id) s

INNER JOIN produit p on p.produit_id = s.produit_id
INNER JOIN produit_stock ps on ps.produit_id=p.produit_id 
LEFT JOIN traduction_produit tp on tp.produit_id = p.produit_id and tp.langue_id = @LgId
LEFT OUTER JOIN tva ON tva.tva_id = p.tva1
WHERE s.nbrProduitsReliees = @nbrProds
ORDER BY p.pref_affichage

DROP TABLE #myprods
DROP TABLE #prodAExclure