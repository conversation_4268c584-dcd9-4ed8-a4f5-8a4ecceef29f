DECLARE @partnerIdProviderType_PasscultureOffreCollective INT = 1
DECLARE @partnerTableRodrigueType_Commande INT = 1

SELECT c.commande_id AS OrderId, i.identite_id AS identityId, i.identite_nom AS identityName
FROM commande c
INNER JOIN identite i ON c.identite_id = i.identite_id
INNER JOIN partner_ids_provider pip 
	ON c.commande_id = pip.rodrigue_id 
	AND pip.partner_ids_provider_type_id = @partnerIdProviderType_PasscultureOffreCollective
	AND pip.partner_table_rodrigue_type_id = @partnerTableRodrigueType_Commande