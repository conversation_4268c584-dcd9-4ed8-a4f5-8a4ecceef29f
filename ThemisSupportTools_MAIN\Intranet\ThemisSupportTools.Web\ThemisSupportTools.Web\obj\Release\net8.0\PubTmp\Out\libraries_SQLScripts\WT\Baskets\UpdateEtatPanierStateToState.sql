﻿/* -------- UpdateEtatPanier.sql --------------


declare @pBasketId int 
set @pBasketId =322676

declare @pEtat varchar(50) 
set @pEtat ='I'
*/

--Declare @Var nvarchar(MAX)= @pListEtatInit /* ex 'I,C' */

if (@pListEtatsInit='ALL')
	update panier set etat=@pEtat, date_operation=GETDATE() where panier_id = @pBasketId
else
begin
	update panier set etat=@pEtat, date_operation=GETDATE() where panier_id = @pBasketId and etat in ({pEtatsInit})
end
if (@pEtat = 'P')
begin
	update panier set etat=@pEtat, date_paiement=GETDATE()  where panier_id = @pBasketId and etat ='P' and date_paiement is null
end
