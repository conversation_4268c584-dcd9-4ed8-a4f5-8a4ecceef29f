﻿
update entree_[eventID] set dateoperation=getdate(), flag_selection='T[userID]'
WHERE entree_id in ([seatsID]) and seance_id =[sessionID] and entree_etat='L' 
	and (flag_selection is null or flag_selection='')


SELECT e.entree_id, r.pos_y, r.pos_x, r.rang, r.siege, d.denom_nom,  r.type_siege, e.iindex, lieu_physique_id, etage_id, section_id, zone_id, r.orientation
,e.reserve_id, e.categorie_id,convert(int, e.seance_id)	as seance_id, --convert(char,entree_etat) as entree_etat
'' as entree_etat
	FROM entree_[eventID] e
	JOIN REFERENCE_LIEU_PHYSIQUE r
	ON REFERENCE_UNIQUE_PHYSIQUE_ID = REF_UNIQ_PHY_ID
	JOIN DENOMINATION d
	ON r.DENOMINATION_ID = d.DENOM_ID
	WHERE entree_id in ([seatsID]) and seance_id =[sessionID] and entree_etat='L' and flag_selection='T[userID]'

--select * FROM entree_[eventID] WHERE entree_id in ([seatsID]) and seance_id =[sessionID] and entree_etat='L' and flag_selection='T[userID]'

