﻿--DECLARE @pOnlyProductFamillyWithParams BIT = 0
--DECLARE @pLangCode VARCHAR(5) = 'fr'

DECLARE @langueId INT = (SELECT langue_id FROM langue WHERE langue_code = @pLangCode)

If(@pOnlyProductFamillyWithParams = 1)
BEGIN
	SELECT pf.Produit_Famille_ID AS Id,
	IIF(t.Traduction = '' OR t.Traduction IS NULL, pf.Produit_Famille_Nom , t.traduction Collate database_default) AS Name
	FROM Produit_Famille pf
	LEFT JOIN traduction_langue t on t.traduction_id = pf.TraductionNom_ID AND t.langue_id = 1
	WHERE pf.Masquer = 0
	AND 1 = ANY (SELECT DISTINCT
				psf_any.Regroupement_couleur
				FROM Produit_Lien_Sous_Famille plsf_any
				INNER JOIN Produit_Sous_Famille psf_any ON plsf_any.Produit_Sous_Famille_ID = psf_any.Produit_Sous_Famille_ID
				WHERE plsf_any.Produit_Famille_ID = pf.Produit_Famille_ID
				AND psf_any.Masquer = 0)
END
ELSE
BEGIN
	SELECT pf.Produit_Famille_ID AS Id,
	IIF(t.Traduction = '' OR t.Traduction IS NULL, pf.Produit_Famille_Nom , t.traduction Collate database_default) AS Name
	FROM Produit_Famille pf
	LEFT JOIN traduction_langue t on t.traduction_id = pf.TraductionNom_ID AND t.langue_id = 1
	WHERE pf.Masquer = 0
	AND 0 = ALL (SELECT DISTINCT
				psf_any.Regroupement_couleur
				FROM Produit_Lien_Sous_Famille plsf_any
				INNER JOIN Produit_Sous_Famille psf_any ON plsf_any.Produit_Sous_Famille_ID = psf_any.Produit_Sous_Famille_ID
				WHERE plsf_any.Produit_Famille_ID = pf.Produit_Famille_ID
				AND psf_any.Masquer = 0)
END