@using Core.Themis.Libraries.Razor.Common.ViewModels.Toasts

@model ToastSettings

<div id="@Model.Id" 
     class="toast fade align-items-center @Model.ToastClassColor border-0"
     data-wdgt-toast-class-position="@Model.ToastClassPosition"
     data-wdgt-toast-delay="@Model.Delay" 
     role="alert"
     aria-live="assertive"
     aria-atomic="true">
    <div class="d-flex">
        <div class="toast-body">
            @Html.Raw(Model.BodyContent)

            @if(Model.SummaryValidation is not null)
            {
                <ul class="py-0 ps-4 m-1">
                    @foreach(string message in Model.SummaryValidation)
                    {
                    <li>@message</li>
                    }
                </ul>
            }
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-wdgt-dismiss="toast" aria-label="Close"></button>
    </div>
</div>