﻿/*
declare @checkEmailUnicity bit
set @checkEmailUnicity = 0

declare @identiteConnected int
set @identiteConnected = 473


declare @postalTelEmail int
set @postalTelEmail= 6

declare @pemail varchar(50)
set @pemail ='<EMAIL>'


declare @pNom varchar(300) = 'test'
declare @pPrenom varchar(300) =''
declare @pPostalCode varchar(10)=''
declare @pVille varchar(300)=''
declare @pPays varchar(300)=''
declare @pPostalTel1 varchar(300)=''
declare @pPostalTelLib1 int=0
declare @pPostalTel2 varchar(300)=''
declare @pPostalTelLib2 int=0
declare @pPostalTel3 varchar(300)=''
declare @pPostalTelLib3 int=0
declare @pPostalTel4 varchar(300)=''
declare @pPostalTelLib4 int=0
declare @pPostalTel5 varchar(300)=''
declare @pPostalTelLib5 int=0
declare @pPostalTel6 varchar(300)=''
declare @pPostalTelLib6 int=0
declare @pPostalTel7 varchar(300)=''
declare @pPostalTelLib7 int=0
declare @pOperateurId int=5650002
declare @pFiliereid int=9910006
declare @pIdentityComplement varchar(300)=''
declare @pAppellationId int = 2
declare @pDateOfBirthday varchar(500) = '01/01/1900'
declare @pAddress1 varchar(300)=''
declare  @pAddress2 varchar(300)=''
declare @pAddress3 varchar(300)=''
declare @pAddress4 varchar(300)=''
*/

Declare @TblTemp table (Email varchar(250),Identite_id int)

if @checkEmailUnicity = 1 
begin
	insert into @TblTemp
	select top 1 EMail,Identite_id from (
	select 
	case when @postalTelEmail =1 then postal_tel1 else '' end +
	case when @postalTelEmail =2 then postal_tel2 else '' end +
	case when @postalTelEmail =3 then postal_tel3 else '' end +
	case when @postalTelEmail =4 then postal_tel4 else '' end +
	case when @postalTelEmail =5 then postal_tel5 else '' end +
	case when @postalTelEmail =6 then postal_tel6 else '' end +
	case when @postalTelEmail =7 then postal_tel7 else '' end
	as email, *
	from identite
	) s
	where email =@pEmail 
	--and identite_password = ''
	and FicheSupprimer = 'N'
	order by identite_id desc

end

Declare @CountResult int

set @CountResult = (select count(*) from @TblTemp)

if @CountResult >0 
	begin
		--identite existe déjà
		select -1  as nextIdentityId
	
	end

if @CountResult =0 
	begin
		-- Select 'Client Non Trouvé'
	
			declare @nextIdentityId int
						
			UPDATE cpt_client SET compteur = compteur +1, @nextIdentityId=compteur+1; 
			SELECT @nextIdentityId  as nextIdentityId;


			INSERT INTO identite (identite_id,identite_nom,identite_complement,identite_titre_id,appellation_id,identite_date_naissance,postal_rue1
			,postal_rue2,postal_rue3,postal_rue4,postal_cp,postal_ville,postal_region,postal_pays,postal_tel1,postal_tel1_libelle_id,postal_tel2,postal_tel2_libelle_id
			,postal_tel3,postal_tel3_libelle_id,postal_tel4,postal_tel4_libelle_id,postal_tel5,postal_tel5_libelle_id,facture_rue1,facture_rue2
			,facture_rue3,facture_rue4,facture_cp,facture_ville,facture_region,facture_pays,facture_tel1,facture_tel1_libelle_id,facture_tel2,facture_tel2_libelle_id
			,facture_tel3,factue_tel3_libelle_id,facture_tel4,facture_tel4_libelle_id,marqueur_mailing_id,identite_validite_debut,identite_validite_fin
			,identite_v,operateur_id,ref_compta,ref_perso,etiquette1,etiquette2,etiquette3,etiquette4,etiquette5,identite_libre1,identite_libre2,identite_libre3
			,identite_libre4,identite_libre5,postal_tel6,postal_tel6_libelle_id,postal_tel7,postal_tel7_libelle_id,identite_prenom,filiere_id,identite_remise
			,identite_remisedg,montant_credit,montant_debit,FicheSupprimer,statut_financier,appelinterloc_id,
			--identite_groupe_id,,
			IDENTITE_DATE_CREATION,
			IDENTITE_DATE_MODIFICATION,
			--operateurmodif_id,
			facture_tel5,facture_tel5_libelle_id,facture_tel6,facture_tel6_libelle_id,facture_tel7
			,facture_tel7_libelle_id,identite_password)
				 VALUES
			 (@nextIdentityId, @pNom, @pIdentityComplement, 0, @pAppellationId, @pDateOfBirthday, @pAddress1,@pAddress2,@pAddress3,@pAddress4, @pPostalCode, @pVille, '',
			  @pPays,@pPostalTel1,@pPostalTelLib1,@pPostalTel2,@pPostalTelLib2,
			 @pPostalTel3,@pPostalTelLib3, @pPostalTel4,@pPostalTelLib4,@pPostalTel5,@pPostalTelLib5,'','','','','','', '','','','','','','','',
			 '','',0,'01/01/1900','01/01/1900',0,@pOperateurId,'', '','','','','','','','','','','',
			 @pPostalTel6,@pPostalTelLib6, @pPostalTel7,@pPostalTelLib7,@pPrenom,@pFiliereid,0,0,0,0,
			 'N',0,0,
			 --[GROUPE_ID], valeur par defaut 0
			 GETDATE(),GETDATE(),
			 --[OPERATEUR_MODIF_ID],  valeur par defaut 0
			 '',0,'',0,'',0,'')

	end


