﻿


select filiere_nom, tt.type_tarif_nom, cat.categ_nom, 
sum(nbSeats) as nbrSeats, 
sum(puhf * nbSeats) as totalhf,
sum(fu * nbSeats) totalf,

puhf,
fu

from (

select r.TYPE_OPERATION, r.MONTANT1 as puhf, r.<PERSON>ONTANT2 as fu, r.CATEGORIE_ID, COUNT(*) as nbSeats,
r.TYPE_TARIF_ID,r.<PERSON>ON<PERSON>NT3,r.<PERSON>ON<PERSON>NT4,r.<PERSON>ON<PERSON>NT5, r.<PERSON>, r.<PERSON>7,r.<PERSON>ONTANT8,r.<PERSON>ON<PERSON>NT9,r.<PERSON>10,
(
	select top 1 clc.filiere_id from commande_ligne_comp clc inner join commande_ligne cl on cl.commande_ligne_id = clc.commande_ligne_id 
	where clc.seance_id=r.seance_id and clc.dossier_id = r.dossier_id and cl.type_ligne = 'DOS'
) as filiereid
From Recette r Where r.SEANCE_ID = @pSessionId 
and r.TYPE_OPERATION in ('E','A','D') 

GROUP BY r.TYPE_OPERATION, r.MONTANT1, r.MONTANT2, r.MONTANT3,  r.MONTANT4, r.MONTANT5,r.MONTANT6,r.MONTANT7,r.MONTANT8, r.MONTANT9,r.MONTANT10, r.categorie_id , 
r.type_tarif_id,r.seance_id, r.dossier_id 

)  res
inner join filiere f on res.filiereid = f.filiere_id
inner join type_tarif tt on res.type_tarif_id = tt.type_tarif_id
inner join categorie cat on cat.categ_id = res.categorie_id
group by filiere_nom, tt.type_tarif_nom, cat.categ_nom, 
puhf,
fu
, TYPE_OPERATION, MONTANT3,  MONTANT4, MONTANT5,MONTANT6,MONTANT7,MONTANT8, MONTANT9,MONTANT10, categorie_id , res.type_tarif_id, res.type_tarif_id --, res.filiere_id --,res.seance_id --, r.dossier_id 
