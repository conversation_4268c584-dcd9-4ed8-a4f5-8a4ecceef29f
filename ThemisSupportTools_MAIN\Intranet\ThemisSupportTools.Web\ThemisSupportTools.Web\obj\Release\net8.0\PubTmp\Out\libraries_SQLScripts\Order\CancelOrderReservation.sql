﻿
/*
DECLARE @pOrderId INT = 174511
DECLARE @poperatorId INT = 5880001
DECLARE @pposteId INT = 1
*/

DECLARE @myCptPaiement INT
DECLARE @cmdLigneId INT

UPDATE CPT_PAIEMENT  SET compteur = compteur + 1;
SELECT @myCptPaiement = compteur FROM CPT_PAIEMENT

DECLARE CommandeLigneInfos CURSOR SCROLL FOR 

	SELECT cl.commande_ligne_id
	FROM commande c 
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	WHERE c.commande_id = @pOrderId AND type_ligne='DOS'

OPEN CommandeLigneInfos; 
FETCH NEXT FROM CommandeLigneInfos INTO @cmdLigneId;


WHILE @@FETCH_STATUS=0 
BEGIN
	DECLARE @myEventId INT, @mySessionId INT, @myDossierId INT
	SELECT @myEventId = manifestation_id, @mySessionId = seance_id, @myDossierId = dossier_id 
	FROM commande_ligne WHERE commande_ligne_id = @cmdLigneId

	DECLARE @sqlUpdateDossier NVARCHAR(MAX), @sqlUpdateDossierSvg NVARCHAR(MAX)

	SET @sqlUpdateDossier = 
		'UPDATE dossier_' + LTRIM(STR(@myEventId)) + ' SET 
		dossier_v = 4, 
		operateur_id = ' + LTRIM(STR(@poperatorId)) + ', 
		dossier_c = '''', 
		dossier_etat = ''A'', 
		dossier_icone = 4097, 
		dossier_montantpayer = 0, 
		dossier_facture = 0, 
		dossier_client_nom = '''', 
		num_paiement = ' + LTRIM(STR(@myCptPaiement)) + ', 
		date_operation = GETDATE(), 
		operation_id = 0
		WHERE dossier_id = ' + LTRIM(STR(@pdossierid))
	
	PRINT(@sqlUpdateDossier)

	EXEC(@sqlUpdateDossier)

	SET @sqlUpdateDossierSvg = 
		'INSERT INTO dossiersvg_' + LTRIM(STR(@myEventId)) + ' 
		(
			dossier_id, 
			identite_id, 
			commande_id, 
			abon_manif_id, 
			seance_id, 
			dossier_v, 
			operateur_id, 
			dossier_montant, 
			dossier_c, 
			dossier_etat, 
			dossier_icone, 
			dossier_numero, 
			dossier_nbplace, 
			dossier_montantpayer, 
			dossier_facture, 
			dossier_client_nom, 
			num_paiement, 
			date_operation, 
			filiere_id, 
			type_operation, 
			dossier_montant1, 
			dossier_montant2, 
			dossier_montant3, 
			dossier_montant4, 
			dossier_montant5, 
			dossier_montant6, 
			dossier_montant7, 
			dossier_montant8, 
			dossier_montant9, 
			dossier_montant10
		) 
		(
			SELECT 
			dossier_id, 
			identite_id,
			commande_id,
			abon_manif_id, 
			seance_id, 
			dossier_v, 
			operateur_id, 
			dossier_montant, 
			dossier_c, 
			dossier_etat, 
			dossier_icone, 
			dossier_numero, 
			dossier_nbplace, 
			dossier_montantpayer, 
			dossier_facture, 
			dossier_client_nom, 
			num_paiement, 
			date_operation, 
			filiere_id, 
			''REMB'', 
			dossier_montant1, 
			dossier_montant2, 
			dossier_montant3, 
			dossier_montant4, 
			dossier_montant5, 
			dossier_montant6, 
			dossier_montant7, 
			dossier_montant8, 
			dossier_montant9, 
			dossier_montant10  
			FROM dossier_' + LTRIM(STR(@myEventId)) + ' 
			WHERE dossier_id = ' + LTRIM(STR(@pdossierid)) + '
		)

		INSERT INTO histodossier 
		(
			date_operation, 
			operateur_id, 
			manif_id, 
			seance_id, 
			dossier_id, 
			etat, 
			commande_id,
			dossier_v, 
			type
		) 
		(
			SELECT 
			date_operation, 
			operateur_id, 
			' + LTRIM(STR(@myEventId)) + ', 
			seance_id, 
			dossier_id, 
			dossier_etat, 
			commande_id, 
			dossier_v, 
			''DOS''		
			FROM dossier_' + LTRIM(STR(@myEventId)) + ' 
			WHERE dossier_id = ' + LTRIM(STR(@pdossierid)) + '
		)'
	
	PRINT(@sqlUpdateDossierSvg)

	EXEC(@sqlUpdateDossierSvg)

	/* foreach entree_id in dossier */

	DECLARE @sqlGetEntrees NVARCHAR(MAX)
	
	SET @sqlGetEntrees = '
	SELECT 
	seance_id, 
	entree_id, 
	0 
	FROM entree_' + LTRIM(STR(@myEventId)) + ' e 
	INNER JOIN REFERENCE_LIEU_PHYSIQUE ON e.reference_unique_physique_id = REFERENCE_LIEU_PHYSIQUE.REF_UNIQ_PHY_ID 
	WHERE DOSSIER_ID = ' + LTRIM(STR(@pdossierid)) + ' 
	AND SEANCE_ID = ' + LTRIM(STR(@pSeanceId)) + '
	AND ENTREE_ETAT <> ''L'' 
	ORDER BY REFERENCE_LIEU_PHYSIQUE.SECTION_ID, e.categorie_id, REFERENCE_LIEU_PHYSIQUE.ETAGE_ID, REFERENCE_LIEU_PHYSIQUE.RANG, e.entree_id'
	
	DECLARE @tablesMyEntrees TABLE (seance_id INT, entree_id INT, done INT)
	
	INSERT INTO @tablesMyEntrees (seance_id, entree_id, done)
	EXEC sp_executesql @sqlGetEntrees, N'@pdossierid int, @pManifId int, @pSeanceId int',@pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

	DECLARE @myEntreeId INT
	SELECT TOP 1 @myEntreeId = entree_id FROM @tablesMyEntrees WHERE done = 0
	WHILE @myEntreeId > 0 /* *********** for each entree_id in dossier */
	BEGIN
		INSERT INTO recette (manifestation_id,seance_id,entree_id,operateur_id,date_operation,type_operation,date_ouverture_caisse,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,numbillet,
		categorie_id,externe,type_tarif_id, dossier_id, motif) 		
		SELECT manifestation_id,seance_id,entree_id, @poperatorId,GETDATE(), 'A', date_ouverture_caisse,montant1,montant2,montant3,montant4,montant5,montant6,montant7,montant8,montant9,montant10,numbillet,
			categorie_id,externe,type_tarif_id, dossier_id, motif FROM recette
		WHERE entree_id = @myEntreeId AND dossier_id = @myDossierId AND seance_id = @mySessionId AND MANIFESTATION_ID = @myEventId AND type_operation='E'		
	
		UPDATE @tablesMyEntrees SET done = 1 WHERE entree_id = @myEntreeId
		SET @myEntreeId = 0
		SELECT TOP 1 @myEntreeId = entree_id FROM @tablesMyEntrees WHERE done = 0
	END
	
	/* end foreach entree_id in dossier */

	DECLARE @sqlUpdateEntreeSvg NVARCHAR(MAX)

	SET @sqlUpdateEntreeSvg = 
	'INSERT INTO entreesvg_[EVENTID] 
	(
	entree_id,
	seance_id,
	dossier_id,
	categorie_id,
	type_tarif_id,
	numero_billet,
	alotissement_id,
	reserve_id,
	contingent_id, 
	montant1,
	montant2,
	montant3,
	montant4,
	montant5,
	montant6,
	montant7,
	montant8,
	montant9,
	montant10,
	entree_etat,
	dateoperation,
	dossier_v,
	valeur_tarif_stock_id,
	valeurtarifstockversion
	)
	(
	SELECT 
	entree_id,
	seance_id,
	dossier_id,
	categorie_id,
	type_tarif_id,
	numero_billet,
	alotissement_id,
	reserve_id,
	contingent_id, 
	montant1,
	montant2,
	montant3,
	montant4,
	montant5,
	montant6,
	montant7,
	montant8,
	montant9,
	montant10,
	''L'',
	getdate(),
	4, 
	@poperatorId, 
	0 
	FROM entree_[EVENTID]  
	WHERE dossier_id = @pDossierId 
	AND seance_id = @pSeanceId 
	AND ENTREE_ETAT =''R'')'

	SET @sqlUpdateEntreeSvg = REPLACE(@sqlUpdateEntreeSvg, '[EVENTID]', @myEventId)

	EXEC sp_executesql @sqlUpdateEntreeSvg, N'@poperatorId int, @pdossierid int, @pManifId int, @pSeanceId int',@poperatorId =@poperatorId, @pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

	DECLARE @sqlUpdateEntree NVARCHAR(MAX)
	
	SET @sqlUpdateEntree = 
	'UPDATE entree_[EVENTID] SET 
	entree_etat = ''L'', 
	dossier_id = 0, 
	type_tarif_id = 0, 
	valeur_tarif_stock_id = 0, 
	flag_selection = '''',
	montant1 = 0, 
	montant2 = 0, 
	montant3 = 0, 
	montant4 = 0, 
	montant5 = 0, 
	montant6 = 0, 
	montant7 = 0, 
	montant8 = 0, 
	montant9 = 0, 
	montant10 = 0, 
	dateoperation = getdate(), 
	controleacces = null 
	WHERE DOSSIER_ID = @pDossierId 
	AND SEANCE_ID = @pSeanceId'
	
	SET @sqlUpdateEntree = REPLACE(@sqlUpdateEntree, '[EVENTID]', @myEventId)
	
	EXEC sp_executesql @sqlUpdateEntree, N'@poperatorId int, @pdossierid int, @pManifId int, @pSeanceId int',@poperatorId = @poperatorId, @pdossierid = @myDossierId, @pManifId = @myEventId, @pSeanceId = @mySessionId

END /* ******************  end for each dossier */

CLOSE CommandeLigneInfos; 
DEALLOCATE CommandeLigneInfos;
--------------------------- les produits 
DELETE @commande_ligne_done
INSERT INTO @commande_ligne_done (commande_ligne_id, done)
SELECT commande_ligne_id, 0 AS done 
FROM commande_ligne 
WHERE commande_id = @porderId 
AND type_ligne='PRO'


SELECT TOP 1 @myCmdLigneId = commande_ligne_id FROM @commande_ligne_done WHERE done = 0
WHILE @myCmdLigneId > 0 /* *********** for each dossier */
BEGIN
	DECLARE @myDossierProdId INT, @dossier_v INT, @prodCount INT, @prod_stock_id INT
	
	SELECT @myDossierProdId = dossier_id 
	FROM commande_ligne dp 
	WHERE dp.commande_ligne_id = @myCmdLigneId
	
	SELECT @prodCount = nb_produit, @prod_stock_id = dos_prod_stock_id, @dossier_v = dos_prod_v 
	FROM dossier_produit 
	WHERE dos_prod_id = @myDossierProdId 
	
	UPDATE dossier_produit SET 
	DOS_PROD_V = @dossier_v + 1,
	OPERATEUR_ID = @poperatorId,
	DOS_PROD_ETAT = 'A',		
	NUM_PAIEMENT = @myCptPaiement,
	DATE_OPERATION = GETDATE()	
	WHERE DOS_PROD_ID = @myDossierProdId
	
	UPDATE iic SET iic.supprimer = 'O' FROM dossier_produit dp
	INNER JOIN identite_infos_comp iic 
		ON iic.identite_id = dp.identite_id 
		AND iic.id = dp.dos_prod_numero
	INNER JOIN produit p ON p.produit_id = dp.produit_id
	INNER JOIN dossier_produitsvg dpsvg 
		ON dpsvg.dos_prod_id = dp.dos_prod_id 
		AND dpsvg.dos_prod_etat = 'R' 
		AND dpsvg.dos_prod_v = 1 
	WHERE dp.Dos_Prod_ID = @myDossierProdId 
	AND p.infocomp_id > 0 
	AND iic.info_comp_id = p.infocomp_id 
	AND iic.identite_id = dp.identite_id 
	AND CONVERT(date, iic.datecreation) = CONVERT(date, dpsvg.date_operation)  

	UPDATE produit_stock  
	SET restant = restant + @prodCount
	WHERE produit_stock_id = @prod_stock_id;

	UPDATE Dossier_Produit_Entree
	SET etat_place = 'L' , date_operation = GETDATE() 
	WHERE Dos_prod_ID = (@myDossierProdId) 
	AND etat_place <> 'L'
END
