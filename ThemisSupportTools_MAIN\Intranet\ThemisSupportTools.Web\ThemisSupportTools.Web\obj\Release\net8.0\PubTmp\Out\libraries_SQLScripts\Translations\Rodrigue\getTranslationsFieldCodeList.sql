﻿/*
  select id, fieldSpecificCode,description from translate_fieldsCodesList
*/

  
  select tfc.id, fieldSpecificCode, tfc.description, ta.id as area_id, name, global_field_id,  txt_de, txt_en, txt_fr, txt_nl 
  from translate_fieldsCodesList tfc
  inner join translate_areas ta on ta.id = tfc.area_id 
   inner join translate_fieldsGlobalTranslation tfgt on tfgt.id = global_field_id
    order by name, tfc.id