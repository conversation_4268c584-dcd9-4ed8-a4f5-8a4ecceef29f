﻿using Core.Themis.Libraries.Data.Entities.LieuPhysique;
using Core.Themis.Libraries.Data.Repositories.Common.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.Data.Repositories.LieuPhysique.Interface
{
    public interface ILieuPhysiqueRepository : IGenericStructureRepository<LieuPhysiqueEntity>
    {
        Task<List<LieuPhysiqueEntity>> GetNonMaskedLieuxPhysiquesAsync(int structureId);
        Task<List<int>> GetLieuxPhysiquesWithImagesAsync(int structureId);
        Task<bool> TransferPhotoPointageAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId);
        Task<IEnumerable<dynamic>> GenerateReferenceSalleImageXmlWithCustomStructureAsync(int structureId);
        Task<IEnumerable<dynamic>> GenerateReferenceSalleImageXmlByLieuPhysiqueAsync(int structureId, int lieuPhysiqueId);
        Task<bool> TransfereVignetteAsync(int structureId, int sourceLieuPhysiqueId, int targetLieuPhysiqueId);
        Task<(IEnumerable<dynamic> SourceOnly, IEnumerable<dynamic> DestinationOnly)> CompareLieuxPhysiquesAsync(int structureId, int sourceLieuPhysiqueId, int destinationLieuPhysiqueId);
        Task<IEnumerable<dynamic>> GenerateReferenceWebXmlWithVignetteAsync(int structureId);
        Task<IEnumerable<dynamic>> GenerateReferenceWebXmlWithVignetteByLieuPhysiqueAsync(int structureId, int lieuPhysiqueId);
    }
}
