﻿----- remet une commande à un statut défini, 'R': "Réservé" 'P': "Payé" attention . Une fois en 'R', on ne peux plus remettre en 'P'

DECLARE @Commande_ID int 
DECLARE @NumPaiement int 
DECLARE @Dossier_id int
DECLARE @Seance_id int 
DECLARE @Manif_id int
DECLARE @C VARCHAR(8000)
DECLARE @SQL VARCHAR(8000)


DECLARE @Etat varchar(1)


set @Commande_ID = @porderId
set @etat = @petatVoulu  -- Etat voulu


------ Début de la procédure


IF @etat = 'P' 
	BEGIN

	   	  
	print 'Delete de la recette'
	DELETE from recette where recette_id in (
		SELECT recette_id from recette r 
		inner join commande_ligne cl on cl.dossier_id = r.dossier_id and cl.seance_id = r.seance_id
		inner join commande_ligne_comp clc on clc.commande_ligne_id = cl.commande_ligne_id
		WHERE cl.type_ligne = 'DOS' and clc.Etat <> 'R' 
		and cl.commande_id = @Commande_ID
	)

	
	--ouverture du curseur Entree
	print 'ouverture du curseur Entree'
	DECLARE Curseur  cursor for
	SELECT cl.Dossier_id, cl.manifestation_id , cl.seance_id from commande_ligne cl inner join commande_ligne_comp clc on clc.commande_ligne_id = cl.commande_ligne_id
	WHERE cl.commande_id = @Commande_ID and cl.type_ligne = 'DOS' and clc.Etat ='B'

	OPEN curseur

	FETCH curseur into @Dossier_id,@Manif_id,@Seance_id

	--génération de la requête boucle Entree
	WHILE @@fetch_status = 0 
		begin 
			
			print 'Maj commande_ligne_comp'
			SET @SQL = ' update clc set clc.etat = ''P'' from commande_ligne_comp clc inner join commande_ligne cl on cl.commande_ligne_id = clc.commande_ligne_id   
			where clc.commande_id = ' + replace(str(@Commande_ID),' ','') +' and clc.dossier_id = ' + replace(str(@Dossier_id),' ','') +'  and cl.type_ligne = ''DOS'''


			SET @SQL = 'update entree_' + replace(str(@Manif_id),' ','') +' set dateoperation =  (select top 1 dateoperation from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by dateoperation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update entreesvg_' + replace(str(@Manif_id),' ','') +' set dateoperation =  (select top 1 dateoperation from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by dateoperation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
		
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update entree_' + replace(str(@Manif_id),' ','') +' set entree_etat = ''P''  where entree_id in  (select entree_id from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +')'
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''B'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)			

			SET @SQL = 'update Dossier_' + replace(str(@Manif_id),' ','') +' set date_operation =  (select top 1 date_operation from dossiersvg_' + replace(str(@Manif_id),' ','') +' where dossier_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by date_operation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update dossiersvg_' + replace(str(@Manif_id),' ','') +' set date_operation =  (select top 1 date_operation from dossiersvg_' + replace(str(@Manif_id),' ','') +' where dossier_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by date_operation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update Dossier_' + replace(str(@Manif_id),' ','') +' set Dossier_etat = ''P'' , dossier_v = (select top 1 dossier_v from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by dateoperation)  
						where  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from  Dossiersvg_' + replace(str(@Manif_id),' ','') +' where Dossier_etat = ''P''  and type_operation = ''EDIT'' and  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from  Dossiersvg_' + replace(str(@Manif_id),' ','') +' where Dossier_etat = ''B''   and  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)		

			

			FETCH curseur into @Dossier_id,@Manif_id,@Seance_id
		END
	CLOSE curseur
	DEALLOCATE curseur 

	print 'Update Commande_ligne_comp'
	update clc set clc.etat = 'P' from commande_ligne cl inner join commande_ligne_comp clc on clc.commande_ligne_id = cl.commande_ligne_id
	where cl.commande_id = @Commande_ID and cl.type_ligne = 'PRO' and clc.Etat ='B'

	print 'Update Dossier_produit'
	update dossier_produit set dos_prod_etat = 'P' , date_operation = (select top 1 date_operation from dossier_produitsvg where commande_id = @commande_id) where commande_id = @Commande_id

	print 'delete Dossier_produitSVG'
	delete from  dossier_produitsvg where commande_id = @Commande_id and dos_prod_etat = 'B' 


	print 'Delete Recette_produit'
	delete from recette_produit where produit_stock_id in (select dos_prod_id from dossier_produitsvg where commande_id = @Commande_id   )


	print 'Update Dossier_produitSVG'
	Update dossier_produitsvg set dos_prod_etat = 'R' where commande_id =  @Commande_id

	print 'delete Histodossier'
	delete  from histodossier where commande_id = @Commande_id and etat = 'B'

END

IF @Etat = 'R'
	BEGIN
	
	--ouverture du curseur NumPaiement
	declare Curseur  cursor for
	SELECT distinct cc_numpaiement from compte_client where commande_id = @Commande_ID and cc_numpaiement>0 

	open curseur

	FETCH curseur INTO @NumPaiement

	--génération de la requête boucle NumPaiement
	WHILE @@fetch_status = 0 
		BEGIN 
			SET @SQL = 'delete from compte_client where cc_numpaiement = ' + replace(str(@NumPaiement),' ','')+ '  '
			print @SQL
			Exec (@SQL)
			fetch curseur into @NumPaiement
		END
	CLOSE curseur
	DEALLOCATE curseur  


	print 'Delete de la recette'
	DELETE from recette where recette_id in (
	SELECT recette_id from recette r 
	inner join commande_ligne cl on cl.dossier_id = r.dossier_id and cl.seance_id = r.seance_id
	inner join commande_ligne_comp clc on clc.commande_ligne_id = cl.commande_ligne_id
	where cl.type_ligne = 'DOS' and clc.Etat <> 'R'
	and cl.commande_id = @Commande_ID)

	

	--ouverture du curseur Entree
	print 'ouverture du curseur Entree'
	DECLARE Curseur  CURSOR FOR
	SELECT cl.Dossier_id, cl.manifestation_id , cl.seance_id from commande_ligne cl inner join commande_ligne_comp clc on clc.commande_ligne_id = cl.commande_ligne_id
	WHERE cl.commande_id = @Commande_ID and cl.type_ligne = 'DOS' and clc.Etat <>'R'

	OPEN curseur

	FETCH curseur into @Dossier_id,@Manif_id,@Seance_id

	--génération de la requête boucle Entree
	WHILE @@fetch_status = 0 
		BEGIN 
			
			print 'Maj commande_ligne_comp'
			SET @SQL = ' update clc set clc.etat = ''R'' from commande_ligne_comp clc inner join commande_ligne cl on cl.commande_ligne_id = clc.commande_ligne_id   
			where clc.commande_id = ' + replace(str(@Commande_ID),' ','') +' and clc.dossier_id = ' + replace(str(@Dossier_id),' ','') +'  and cl.type_ligne = ''DOS'''

			SET @SQL = 'update entree_' + replace(str(@Manif_id),' ','') +' set dateoperation =  (select top 1 dateoperation from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by dateoperation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update entreesvg_' + replace(str(@Manif_id),' ','') +' set dateoperation =  (select top 1 dateoperation from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by dateoperation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
		
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update entree_' + replace(str(@Manif_id),' ','') +' set entree_etat = ''R''  where entree_id in  (select entree_id from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +')'
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from entreesvg_' + replace(str(@Manif_id),' ','') +' where entree_etat <> ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update entreesvg_' + replace(str(@Manif_id),' ','') +' set entree_etat = ''R'',dossier_v = 1  where  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update Dossier_' + replace(str(@Manif_id),' ','') +' set date_operation =  (select top 1 date_operation from dossiersvg_' + replace(str(@Manif_id),' ','') +' where dossier_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by date_operation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update dossiersvg_' + replace(str(@Manif_id),' ','') +' set date_operation =  (select top 1 date_operation from dossiersvg_' + replace(str(@Manif_id),' ','') +' where dossier_etat = ''P'' and dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' order by date_operation)
						where dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +''
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update Dossier_' + replace(str(@Manif_id),' ','') +' set Dossier_etat = ''R'' , num_paiement = 0 ,dossier_icone = 0 ,dossier_v = 1 where  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from  Dossiersvg_' + replace(str(@Manif_id),' ','') +' where Dossier_etat = ''R''  and  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from  Dossiersvg_' + replace(str(@Manif_id),' ','') +' where Dossier_etat = ''B''   and  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'delete from  Dossiersvg_' + replace(str(@Manif_id),' ','') +' where type_operation = ''EDIT''   and  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			SET @SQL = 'update dossiersvg_' + replace(str(@Manif_id),' ','') +' set Dossier_etat= ''R'', type_operation = ''RESA'', num_paiement = 0 ,dossier_icone = 0 ,dossier_v = 1 where  dossier_id = ' + replace(str(@Dossier_id),' ','') +' and seance_id = ' + replace(str(@Seance_id),' ','') +' '
			print @SQL
			Exec (@SQL)

			FETCH curseur into @Dossier_id,@Manif_id,@Seance_id
		END
	CLOSE curseur
	DEALLOCATE curseur 

	print 'Update Commande_ligne_comp'
	update clc set clc.etat = 'R' from commande_ligne cl inner join commande_ligne_comp clc on clc.commande_ligne_id = cl.commande_ligne_id
	where cl.commande_id = @Commande_ID and cl.type_ligne = 'PRO' and clc.Etat <>'R'

	print 'Update Dossier_produit'
	update dossier_produit set dos_prod_etat = 'R' , date_operation = (select top 1 date_operation from dossier_produitsvg where commande_id = @commande_id) where commande_id = @Commande_id

	print 'delete Dossier_produitSVG'
	delete from  dossier_produitsvg where commande_id = @Commande_id and dos_prod_etat <> 'P' 

	print 'Delete Recette_produit'
	delete from recette_produit where produit_stock_id in (select dos_prod_id from dossier_produitsvg where commande_id = @Commande_id   )

	print 'Update Dossier_produitSVG'
	Update dossier_produitsvg set dos_prod_etat = 'R' where commande_id =  @Commande_id

	print 'delete Histodossier'
	delete  from histodossier where commande_id = @Commande_id and etat <> 'R'

END

