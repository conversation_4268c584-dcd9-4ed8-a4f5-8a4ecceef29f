﻿declare @myEventId int = @pEventId
declare @mySessionId int = @pSessionId

declare @tableSessions table (session_id int) 
if (@mySessionId = 0)
begin
insert into @tableSessions select seance_id from seance where seance.manifestation_id = @myEventId
end
else
begin
insert into @tableSessions select seance_id from seance where seance.seance_Id = @mySessionId
end


SELECT m.manifestation_id, m.manifestation_nom, convert(int,s.seance_id) as seance_id, s.seance_date_deb, type_operation , CONVERT(int, montant1 * 100) as montantHFrais, 
CONVERT(int, montant2 * 100) as montant<PERSON>rais, cat.categ_id, 
cat.categ_nom, tt.type_tarif_id, tt.type_tarif_nom

--montant3 ,*
FROM recette r 
inner join categorie cat on cat.categ_id = r.categorie_id 
inner join type_tarif tt on tt.type_tarif_id = r.type_tarif_id
inner join seance s on s.seance_Id = r.seance_id
inner join manifestation m on m.manifestation_id = r.manifestation_id and m.manifestation_id = s.manifestation_id
inner join @tableSessions myS on myS.session_id = s.seance_Id
WHERE 
 recette_id = (select MAX(recette_id) from recette r2 where r2.seance_id = r.seance_id and r2.entree_id = r.entree_id)
and type_operation in ('E','D')
order by recette_id desc