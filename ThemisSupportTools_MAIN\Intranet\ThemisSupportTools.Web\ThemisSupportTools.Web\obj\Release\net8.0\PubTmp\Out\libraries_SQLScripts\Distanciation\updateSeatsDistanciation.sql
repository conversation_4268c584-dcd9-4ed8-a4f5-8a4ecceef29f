﻿
declare @sessId int = @session_id /* recu en param */


declare @nbrPlacesTampon int  = dbo.getDistanciation(@sessId)
declare @nbrPlacesTamponUpDown int  = dbo.getDistanciationUpAndDown(@sessId)

	DECLARE @myEntreeid int, @lieu_physique_id int, @zone_id int, @etage_id int, @section_id int
	DECLARE @myOrientation VARCHAR(2)

	declare @entreesCovid table (pox_x int, pos_y int , iindex int)


	DECLARE cur_myentrees CURSOR SCROLL FOR
		SELECT entree_id, rlp.orientation, rlp.lieu_physique_id, rlp.zone_id, rlp.etage_id, rlp.section_id FROM entree_[EVENTID] e 
		inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id		
		where entree_id in ([LISTSEATSID]) and seance_id = @sessId

	OPEN cur_myentrees
	FETCH NEXT FROM cur_myentrees INTO @myEntreeid, @myOrientation, @lieu_physique_id, @zone_id, @etage_id, @section_id 
	WHILE @@FETCH_STATUS = 0
	BEGIN
		select @myEntreeid,@myOrientation
		
		

		if (@myOrientation = 'S' or @myOrientation = 'N')
		begin
			declare @pos_x_avant int
			declare @pos_x_apres int
			declare @pos_y_rang int
			declare @pos_y_min int
			declare @pos_y_max int
			
			 select @pos_x_avant = pos_x-@nbrPlacesTampon,
				@pos_x_apres = pos_x+@nbrPlacesTampon,
				@pos_y_rang = pos_y,
				@pos_y_min = pos_y - @nbrPlacesTamponUpDown,
				@pos_y_max = pos_y + @nbrPlacesTamponUpDown
				
			  FROM entree_[EVENTID] e inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex		
			where entree_id = @myEntreeid and seance_id = @sessId 

			insert into @entreesCovid select rlp.pos_x, rlp.pos_y, rlp.iindex from 
			entree_[EVENTID] e 
			inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex
				WHERE 				
				rlp.pos_y = @pos_y_rang
				and rlp.POS_X >= @pos_x_avant 
				and rlp.POS_X <= @pos_x_apres
				
				and rlp.lieu_physique_id = @lieu_physique_id and rlp.zone_id=@zone_id and rlp.etage_id = @etage_id and rlp.section_id = @section_id			
				and e.entree_id not in ([LISTSEATSID])
				and rlp.iindex not in (select iindex from @entreesCovid)
				and e.seance_id = @sessId
			
			insert into @entreesCovid select rlp.pos_x, rlp.pos_y, rlp.iindex from 
			entree_[EVENTID] e 
			inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex
				WHERE 
				rlp.pos_y >= @pos_y_min and rlp.pos_y <= @pos_y_max	/* les places en haut en bas du même rang QUE LES PLACES VRAIEMENT PRISES */		
				and rlp.POS_x >= @pos_x_avant + @nbrPlacesTampon    
				 and rlp.POS_x <= @pos_x_apres - @nbrPlacesTampon				
				and rlp.lieu_physique_id = @lieu_physique_id and rlp.zone_id=@zone_id and rlp.etage_id = @etage_id and rlp.section_id = @section_id			
				and e.entree_id not in ([LISTSEATSID])
				and rlp.iindex not in (select iindex from @entreesCovid)
				and e.seance_id = @sessId			

				
				
		end
		else
		begin
			/* E ou O */
			declare @pos_y_avant int
			declare @pos_y_apres int
			declare @pos_x_min int
			declare @pos_x_max int
			declare @pos_x_rang int

			select @pos_y_avant = pos_y-@nbrPlacesTampon,
				@pos_y_apres = pos_y+@nbrPlacesTampon,
				@pos_x_min = pos_x - @nbrPlacesTamponUpDown,
				@pos_x_max = pos_x - @nbrPlacesTamponUpDown,
				@pos_x_rang = pos_x
				
			  FROM entree_[EVENTID] e inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex		
			where entree_id = @myEntreeid and seance_id = @sessId 

			insert into @entreesCovid select rlp.pos_x, rlp.pos_y, rlp.iindex from 
			entree_[EVENTID] e 
			inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex
				WHERE 
				rlp.pos_x >= @pos_x_min
				and rlp.pos_x <= @pos_x_max				
				and rlp.POS_y >= @pos_y_avant 
				and rlp.POS_y <= @pos_y_apres
				and rlp.lieu_physique_id = @lieu_physique_id and rlp.zone_id=@zone_id and rlp.etage_id = @etage_id and rlp.section_id = @section_id			
				and e.entree_id not in ([LISTSEATSID])
				and rlp.iindex not in (select iindex from @entreesCovid)
				and e.seance_id = @sessId
				
			insert into @entreesCovid select rlp.pos_x, rlp.pos_y, rlp.iindex from entree_[EVENTID] e 
			inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex
				WHERE 				
				rlp.pos_x >= @pos_x_min 
				and rlp.pos_x <= @pos_x_max	/* les places en haut en bas du même rang QUE LES PLACES VRAIEMENT PRISES */		
				and rlp.POS_y >= @pos_y_avant + @nbrPlacesTampon    
				 and rlp.POS_y <= @pos_y_apres - @nbrPlacesTampon

				and rlp.lieu_physique_id = @lieu_physique_id and rlp.zone_id=@zone_id and rlp.etage_id = @etage_id and rlp.section_id = @section_id			
				and e.entree_id not in ([LISTSEATSID])
				and rlp.iindex not in (select iindex from @entreesCovid)
				and e.seance_id = @sessId				
				

			--select @pos_x_avant, @pos_x_apres, @pos_y_rang




		end
		FETCH NEXT FROM cur_myentrees INTO @myEntreeid,@myOrientation, @lieu_physique_id, @zone_id, @etage_id, @section_id 
	END
	CLOSE cur_myentrees
	DEALLOCATE cur_myentrees
		
	declare @alotCovid int = (select preference_valeur FROM structure_prefs WHERE preference_cle='DISTANCIATION_ALOT')

	/**************** changer ici ce qu'on fait des places de distanciation ***************/
	
	update entree_[EVENTID] set alotissement_id = @alotCovid , dateoperation =getdate(), flag_selection =''  where entree_id in (
		select entree_id from @entreesCovid eC
		inner join entree_[EVENTID] e on ec.iindex = e.iindex
		inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id = e.reference_unique_physique_id and rlp.iindex = e.iindex
		and e.seance_id = @sessId
	) and entree_etat='L' and (flag_selection is null or flag_selection ='' or flag_selection like 'C[0-9]%')