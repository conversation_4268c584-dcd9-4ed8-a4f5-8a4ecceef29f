﻿	
	/*
declare @pEventId int = 1
declare @pOrderId int = 1
declare @pSeatId int = 1
declare @pIdentityId int = 1
*/
	
	declare @colForDP varchar(2) = '5' /* renseigner ici la colonne de vts_grille oÄ†Â¹ est inscrit le montant Ä†  mettre en acompte */ 
	declare @dateMontantLibreStart datetime = '01/03/2020' /* START si la seance est entre START et END : l'internaute peut choisir son montant */ 
	declare @dateMontantLibreEnd datetime = '01/01/2020' /* END si la seance est entre START et END : l'internaute peut choisir son montant */ 
	declare @checkRib varchar(1) = 'N' /* si O, l'internaute doit avoir un rib renseigne pour effectuer JyVaisPlus */ 
	 
	/* **** si la colonne ou prendre le montant est renseignee dans depotvente_param, la variable est reecrite :  */ 
	IF EXISTS(select * from sys.columns  
		WHERE Name = N'col_grille' and Object_ID = Object_ID(N'depotvente_param')) 
	BEGIN 
		SELECT @colForDP = col_grille FROM depotvente_param		 
	END 
	 




    
   DECLARE @typereprise varchar(10) 
   SET @typereprise = 'chosen' 
 
   DECLARE @mySeance decimal(18,0) 
    
     
DECLARE @siteId int  
DECLARE @SQL2 nvarchar(max) = N'SELECT TOP 1 @seaId = seance_id FROM entree_' + LTRIM(STR(@pEventId)) + ' e where e.entree_id =' + LTRIM(STR(@pSeatId)) ; 
exec sp_executesql @SQL2, N'@seaId int out', @mySeance out 
 
/* calcul du type de reprise suivant la seance : date de seance ou autres */ 
SELECT @typereprise = ( 
SELECT case when s.seance_date_deb> @dateMontantLibreStart and s.seance_date_deb< @dateMontantLibreEnd then 'chosen' else 'fixed' end as typeReprise 
FROM seance s where seance_id = @mySeance) 
 
	 
declare @checkedRib int =1 
/* pas de check rib ici, en commentaire : */ 
IF (@checkRib='O')  
BEGIN 
	SELECT @checkedRib = count(*) from identite_rib WHERE identite_id=@pIdentityId and actif=1 and iban<>'' 
END 

IF (@checkedRib =1) 
BEGIN 

  DECLARE @SQL VARCHAR(4000)  
  SET @SQL ='SELECT top 1 convert(int,(vts.vts_grille' + @colForDP + ')*100) as montantReprise,''' + @typereprise + ''' as typeReprise,  ''grille' + @colForDP + ' de ttid ''+ LTRIM(STR(vts.type_tarif_id)) + '' vtsV='' + LTRIM(STR(vts.vts_v)) as spComm  
  ,0 as spVersion ,
  CAST(s.seance_id AS INT) as seance_id, 
  dsvg.dossier_id,
  dsvg.dossier_v

  FROM dossiersvg_' + LTRIM(STR(@pEventId)) + ' dsvg   
  INNER JOIN entreesvg_' + LTRIM(STR(@pEventId)) + ' esvg ON esvg.dossier_id = dsvg.dossier_id AND dsvg.dossier_v=esvg.dossier_v  
  INNER JOIN commande_ligne cl on cl.dossier_id =dsvg.dossier_id and cl.commande_id=' + LTRIM(STR(@pOrderId)) + ' AND dsvg.identite_id=cl.identite_id  
  INNER JOIN seance s on esvg.seance_id=s.seance_Id  
  INNER JOIN valeur_tarif_stock' + LTRIM(STR(@pEventId)) + ' vts ON vts.type_tarif_id=esvg.type_tarif_id AND vts.seance_id=s.seance_Id  
  AND vts.categ_id = esvg.categorie_id  
  WHERE --dsvg.identite_id = ' + LTRIM(STR(@pIdentityId)) + ' AND 
	s.seance_date_deb > getdate() AND
	dsvg.dossier_etat in (''P'',''B'') AND
	esvg.entree_id=' + LTRIM(STR(@pSeatId)) + '  
			AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_' + LTRIM(STR(@pEventId)) + ' esvg2   
			WHERE   
		 esvg.entree_id=esvg2.entree_id	and esvg2.dossier_id=esvg.dossier_id  
		 AND esvg.seance_id=s.seance_Id  
  )  
   --obtenir la derniere valeur (=celle en cours) du vts  
  AND vts.vts_v= (  
		SELECT MAX(vts_v) FROM valeur_tarif_stock' + LTRIM(STR(@pEventId)) + ' vts2   
		WHERE vts2.type_tarif_id=vts.type_tarif_id AND vts2.categ_id=vts.categ_id AND vts2.seance_id=vts.seance_id)'  

	PRINT @SQL  
	EXEC (@SQL)  
END 
ELSE 
BEGIN 
  	SELECT -2 as montantReprise,'notrib' as spComm,  
		(select count(*) FROM ProcedureChanges WHERE ObjectNAME='DEPOTVENTE_GetMontantReprise') as spVersion  
END 
	  
 