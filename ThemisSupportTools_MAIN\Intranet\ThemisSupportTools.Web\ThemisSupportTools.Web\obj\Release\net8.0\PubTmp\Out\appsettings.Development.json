{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",


  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_dev;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    //  "WsAdminDBProd": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    // "WsAdminDBTest": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "QueuingDB": "Server=*************;Database=WSAdmin_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "ThemisSupportTools": "Server=*************;Database=WSAdmin_dev;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl",
    "DEV": {
      "WsAdminDB": "Server=*************;Database=WSAdmin_dev;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true"
    },
    "TEST": {
      "WsAdminDB": "Server=*************;Database=WSAdmin_dev;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true"
    },
    "PROD": {
      "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true"
    }
  },

  "ServiceRattrapePaths": {
    "PreProdPath": "D:\\applis\\servicesRattrapeP\\dev",
    "ProdPath": "D:\\applis\\servicesRattrapeP\\dev"
  },
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "TSTConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\DEV\\{structureId}\\CONFIGSERVER\\config.ini.xml",
  "CustomerFilesPath": "\\\\Srv-paiement64\\customerfiles\\{environment}",
  "ResourcesFilesOfPlatformsPath": "\\\\**************\\customerfiles\\{environment}\\default\\{platform}\\RESOURCES",
  "ImagesSeatingPlansPath": "\\\\**************\\customerfiles\\{environment}\\{structureId}\\INDIV\\IMAGES\\seatingplans",
  "DetailedErrors": true,
  "timerInMinute": 5,
  "TypeRun": "TEST",
  "ENVIRONMENT": "DEV",
  "widgetAdminUrl": "https://dev2.themisweb.fr/admin/v1/widget-js"
}
