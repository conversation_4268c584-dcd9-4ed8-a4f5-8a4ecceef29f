﻿	--declare @delayTo int = 10
	--declare @delaySince int = 6000000
	SELECT * FROM (
	SELECT
	loeref.code, lec.etat as lecEtat, lec.etape_id,  p.panier_id, structure_id, act.action_id, act.typePaiement, p.date_operation, p.date_paiement, log_etape_id as lastMessageId, lec.date_operation as lastMessageDate,
	identite_id, web_user_id, p.etat, commande_id, p.transaction_id, p.certificate, p.card_number, p.card_type, email, adresse_livraison_id, p.email_error
	FROM panier p 
	 LEFT OUTER JOIN logs_etapes_creationCmds lec 
	 ON lec.panier_id = p.panier_id and log_etape_id = (SELECT max(log_etape_id) FROM logs_etapes_creationCmds lec2 WHERE lec2.panier_id = p.panier_id) 
	 LEFT OUTER JOIN logs_etapes_creationCmds_reference loeref ON loeref.etape_id = lec.etape_id 
	LEFT OUTER JOIN panier_action_payment act  ON act.panier_id = p.panier_id and act.etat='P'

	 WHERE p.etat='P'
	AND p.date_paiement<dateadd(second, -@delayTo,getdate()) 
	AND p.date_paiement>dateadd(second, -@delaySince,getdate())
	AND structure_id IN ([structuresId])
	) s 
	 WHERE lecEtat=99 and email_error is null ORDER BY panier_id desc