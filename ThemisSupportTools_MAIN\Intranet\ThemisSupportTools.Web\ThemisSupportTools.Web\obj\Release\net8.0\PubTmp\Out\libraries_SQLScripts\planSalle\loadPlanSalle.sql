﻿/********* LOAD PLAN SALLE *********/
declare @pWebUserId int 
set @pWebUserId = [webUserId]

declare @pSessionId int 
set @pSessionId = [sessionID]

declare @pZoneId int 
set @pZoneId = [zoneID]

declare @pFloorId int 
set @pFloorId = [floorID]

declare @pSectionId int 
set @pSectionId = [sectionID]



DECLARE @SQL VARCHAR(max)
SET @SQL = 'SELECT  seance_id, e.entree_id,
rlp.siege as seat, rlp.rang as row, rlp.orientation, rlp.type_siege, pos_x, pos_y 
, decal_x, decal_y, orientation, 
z.zone_id, et.etage_id,sect.section_id,
cat.categ_id,
CASE WHEN
entree_etat=''L'' AND (flag_selection is null or flag_selection='''')
AND e.contingent_id=0 AND e.alotissement_id=0
THEN ''O'' ELSE ''N'' END as IsFree, '
   
if @pWebUserId = 0
begin
	set @SQL += ' ''N''  as IsMine '
end

if @pWebUserId > 0
begin 
	set @SQL += 'case when flag_selection like ''%'+CONVERT(varchar(max), @pWebUserId)+'%'' then ''O'' else ''N'' end as IsMine '
end

SET @SQL += ',rlp.iindex
,e.reserve_id
,rlp.denomination_id, d.denom_nom, rlp.bordure, e.entree_etat, e.alotissement_id, e.contingent_id, rlp.tribune as tribune_id,  rlp.porte as porte_id, rlp.acces as acces_id
, z.zone_nom, et.etage_nom, sect.section_nom , cat.categ_nom, res.reserve_nom, alot.alot_nom, co.conting_nom,  pptrib.nom as tribune, ppacces.nom as acces, ppporte.nom as gate, e.lieu_configuration_id as lieu_id
FROM entree_[eventID] e
INNER JOIN reference_lieu_physique rlp ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id
INNER JOIN denomination d on d.denom_id = rlp.denomination_id
INNER JOIN zone z ON z.zone_id = rlp.zone_id 
INNER JOIN etage et ON et.etage_id = rlp.etage_id 
INNER JOIN section sect ON sect.section_id = rlp.section_id
INNER JOIN categorie cat ON cat.categ_id=e.categorie_id

LEFT OUTER JOIN reserve res ON res.reserve_id =e.reserve_id
LEFT OUTER JOIN alotissement alot ON alot.alot_id = e.alotissement_id
LEFT OUTER JOIN contingent co ON co.conting_id = e.contingent_id

LEFT OUTER JOIN Propriete_physique pptrib ON pptrib.id = rlp.tribune 
LEFT OUTER JOIN Propriete_physique ppacces ON ppacces.id = rlp.acces 
LEFT OUTER JOIN Propriete_physique ppporte ON ppporte.id = rlp.porte 

WHERE entree_etat<>''X'' and entree_etat<>''I'' 
AND e.seance_id=' + CONVERT(varchar(50), @pSessionId) 

 
 if @pZoneId > 0
 begin
	set @SQL += ' AND z.zone_id = '+ CONVERT(varchar(50), @pZoneId)
 end
 
  if @pFloorId > 0
 begin
	set @SQL += ' AND et.etage_id = '+ CONVERT(varchar(50), @pFloorId)
 end

  if @pSectionId > 0
 begin
	set @SQL += ' AND sect.section_id = '+ CONVERT(varchar(50), @pSectionId)
 end

 print @sql
 EXEC(@SQL)

