@using Core.Themis.Libraries.Razor.Common.ViewModels.Modals
@using Core.Themis.Libraries.BLL.Helpers

@model InputFileModalSettings

@{
    string _controlRangeId = Guid.NewGuid().ToString();
    string _containCheckboxId = Guid.NewGuid().ToString();
    string _imgId = Guid.NewGuid().ToString();
}

<div class="widget-modal fade" tabindex="-1" id="@Model.ModalId" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <form id="@Model.Form.FormId">
                <div class="modal-header">
                    <h5 class="modal-title" id="@(Model.ModalId)label">Personnalisation d'image</h5>
                    <button type="button"
                            class="btn-close"
                            aria-label="Close"
                            onclick="sendCustomModalResponse('@Model.IframeSelector', '@Model.ModalId', null)">
                    </button>
                </div>
                <div class="modal-body">
                    <div style="display:flex; gap:1rem;">
                        <div style="align-self-center;overflow: auto;flex-grow: 1;text-align: center;">
                            <img id="@_imgId" src="@Model.ImageDataUrl" style="margin:auto;"/>
                        </div>
                        <div style="margin-left: auto;" >
                            <div class="form-group">
                                <label for="@_controlRangeId">Taille</label>
                                <input id="@_controlRangeId"
                                       name="@(nameof(Model.Form.SizeRangeValue))"
                                       value="@(Model.Form.SizeRangeValue == 0 ? Model.RangeMin : Model.Form.SizeRangeValue)"
                                       type="range"  
                                       class="form-control-range" 
                                       min="@Model.RangeMin" 
                                       max="@Model.RangeMax" 
                                       step="@Model.RangeStep" 
                                       onchange="handleChangeCustomizeImageProperties()">
                            </div>
                            <div class="form-check pl-0">
                                <input id="@_containCheckboxId"
                                       class="form-check-input" 
                                       type="checkbox" 
                                       name="@(nameof(Model.Form.IsContainMode))"
                                       checked="@Model.Form.IsContainMode" 
                                       onclick="handleChangeCustomizeImageProperties()">
                                <label class="form-check-label" for="@_containCheckboxId">Contain mode</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger" type="button" onclick="sendCustomModalResponse('@Model.IframeSelector', '@Model.ModalId', null)">Annuler</button>
                    <input class="btn btn-primary" type="submit" value="Valider">
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    async function handleChangeCustomizeImageProperties() {
        let isContainMode = document.getElementById('@_containCheckboxId').checked;
        let newSize = document.getElementById('@_controlRangeId').value;
        let newImage = await handleCallDotNetMethodAsync('@Model.IframeSelector', 'dotNetInputImageFileHelper', 'HandleChangeCustomizeImageProperties', parseInt(newSize, 10), isContainMode);

        document.getElementById('@_imgId').src = newImage;
    }
</script>
