﻿
--DECLARE @PartnerId int 
BEGIN TRANSACTION
	BEGIN TRY

		--- supprimer les structures qui ne sont plus dans la liste
		update structureAcceptPartenaire SET supp='O', date_supp=getdate() 
		WHERE partenaire_id =@pPartnerId and structure_id NOT IN @plistStructures AND date_supp IS NULL

		--- insere les structures qui sont dans la liste (et qui n'etaient pas déjà reliées)
		INSERT INTO structureAcceptPartenaire(partenaire_id,structure_id) 
		 SELECT @pPartnerId ,structure_id from structures where  structure_id in  @plistStructures
		 AND structure_id NOT IN (select structure_id from structureAcceptPartenaire where partenaire_id = @pPartnerId AND supp IS NULL)
	

	COMMIT
	END TRY

	BEGIN CATCH

	select 'error'
	ROLLBACK;
	 THROW; 
END CATCH
