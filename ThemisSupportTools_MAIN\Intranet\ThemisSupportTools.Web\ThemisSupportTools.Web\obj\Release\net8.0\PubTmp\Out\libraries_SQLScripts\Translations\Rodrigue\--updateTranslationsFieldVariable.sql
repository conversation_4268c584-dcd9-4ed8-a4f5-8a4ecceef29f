﻿/*

  declare @pFieldId int
declare @pVariableId int
*/


--check si le champ existe
declare @checkIsFieldExist int 
  set @checkIsFieldExist = (select count(*) from translate_fieldsGlobalTranslation where Id =@pFieldId )


declare @checkIsFieldVariableExist int 
  set @checkIsFieldVariableExist = (select count(*) from translate_fieldsVariables where fieldId =@pFieldId and variableId = @pVariableId )


if @pFieldId <> '' and @pVariableId <> '' and @checkIsFieldVariableExist = 0  and @checkIsFieldExist > 0
    update  translate_fieldsVariables set (@pFieldId, @pVariableId) 
   