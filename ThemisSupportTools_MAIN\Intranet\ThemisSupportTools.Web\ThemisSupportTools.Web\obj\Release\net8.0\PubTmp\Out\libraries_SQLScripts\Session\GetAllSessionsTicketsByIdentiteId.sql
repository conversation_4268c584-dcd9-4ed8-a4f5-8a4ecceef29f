-- declare @pidentiteId int = 47

declare @thisManifId int

declare cur_manifs cursor for
--select distinct manifestation_id from commande_ligne where identite_id = @pidentiteId 
select distinct cl.manifestation_id from commande_ligne cl
where cl.manifestation_id>0 and cl.seance_id>0 and identite_id = @pidentiteId 

declare @SQL varchar(max)
open cur_manifs
fetch cur_manifs into @thisManifId

while @@fetch_status = 0
	begin
		set @SQL = 'SELECT distinct 
             e.entree_id
             , esvg.dossier_id
             , e.seance_id
             , esvg.type_tarif_id
             , e.categorie_id           

             ,rlp.rang
			,rlp.siege
			,rlp.denomination_id
			,den.denom_nom

             , esvg.montant1 * 100 + esvg.montant2 * 100 +         
             case  when modecol4=''REMISE''  then - esvg.montant4* 100 
                           when modecol4=''TAXE'' then + esvg.montant4* 100 
                           else 0 END 
                           +
             case  when modecol5=''REMISE''  then - esvg.montant5 * 100 
                           when modecol5=''TAXE'' then + esvg.montant5* 100 
                           else 0 END +
             case  when modecol6=''REMISE''   then - esvg.montant6 * 100 
                           when modecol6=''TAXE'' then + esvg.montant6* 100 
                           else 0 END +
             case  when modecol7=''REMISE''  then - esvg.montant7 * 100 
                           when modecol7=''TAXE'' then + esvg.montant7* 100 
                           else 0 END +
             case  when modecol8=''REMISE'' then - esvg.montant8 * 100 
                           when modecol8=''TAXE'' then + esvg.montant8* 100 
                           else 0 END +
             case  when modecol9=''REMISE''  then - esvg.montant9 * 100 
                           when modecol9=''TAXE'' then + esvg.montant9* 100 
                           else 0 END +
             case  when modecol10=''REMISE''  then - esvg.montant10 * 100 
                           when modecol10=''TAXE'' then + esvg.montant10* 100 
                           else 0 END  as amount
             , esvg.entree_etat  
             ,dsvg.identite_id
             ,dsvg.commande_id 
             ,e.reference_unique_physique_id 
             , dsvg.date_operation
             FROM dossiersvg_'+ replace(str(@thisManifId),' ','') +' dsvg                  
            INNER JOIN entreesvg_'+ replace(str(@thisManifId),' ','') +' esvg on esvg.seance_id = dsvg.seance_id and esvg.dossier_id = dsvg.dossier_id and dsvg.dossier_v=esvg.dossier_v
            INNER JOIN entree_'+ replace(str(@thisManifId),' ','') +' e ON esvg.entree_id=e.entree_id
            INNER JOIN reference_lieu_physique rlp ON rlp.ref_uniq_phy_id = e.reference_unique_physique_id and e.iindex = rlp.iindex
			INNER JOIN denomination den ON den.denom_id = rlp.denomination_id

            INNER JOIN structure struc on 1=1                     
             WHERE dsvg.identite_id='+ replace(str(@pidentiteId),' ','') +'                    
             AND esvg.dossier_v = (select MAX(esvg2.dossier_v) FROM entreesvg_'+ replace(str(@thisManifId),' ','') +' esvg2                
                                                                   WHERE esvg2.entree_id=esvg.entree_id    
                                                                   and esvg2.dossier_id=esvg.dossier_id
                                                                                 and esvg2.seance_id=esvg.seance_Id  
                                                                                 and esvg2.entree_etat <>''F'' /* elimine les entrees en mode Facture */
                           )            
             AND (dsvg.dossier_etat <> ''P'' or dsvg.type_operation <> ''EDIT'')  /* elimine les lignes en P et EDIT */
             and  dsvg.type_operation <> ''DUPLI'' '

             print @sql

			exec (@SQL)

			fetch cur_manifs into @thisManifId
	end
close cur_manifs
deallocate cur_manifs  


--select *

--select*  from identite order by identite_id desc