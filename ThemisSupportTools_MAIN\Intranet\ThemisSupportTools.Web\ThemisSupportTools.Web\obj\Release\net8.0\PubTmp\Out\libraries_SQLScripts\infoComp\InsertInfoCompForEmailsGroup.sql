﻿/* InsertInfoCompForEmailGroup.sql */
/*
Declare @email Varchar(50) =  '<EMAIL>'

*/
	declare @identite_id int
	declare @info_comp_id int

	select @identite_id=identite_id from identite where postal_tel[COLEMAIL] = @email AND FicheSupprimer = 'N' 

	select @info_comp_id = info_comp_id from info_comp where valeur_param1  = @groupId

	DECLARE @ICEXIST INT;
	SELECT @ICEXIST=count(*) FROM identite_infos_comp WHERE identite_id=@identite_id AND info_comp_id=@info_comp_id

	IF @info_comp_id IS NOT NULL
	BEGIN

		IF (@ICEXIST=0)
			INSERT INTO identite_infos_comp  VALUES (
				@identite_id, @info_comp_id,'','','','','N',getdate(),getdate());

		 ELSE 
			UPDATE identite_infos_comp SET supprimer='N' , datemodification=getdate() 
			WHERE identite_id=@identite_id AND info_comp_id=@info_comp_id;
	END