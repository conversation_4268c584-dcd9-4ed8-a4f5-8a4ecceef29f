﻿
/*****

declare @pPanierId int = 110264
declare @pproduitid int = 4260231
declare @pproduitnom varchar = 'Studi-Flat 23'
declare @pnbr int = 1
declare @pamount int = 1000
declare @pfees int = 0
declare @ptype_ligne varchar  = 'CADH'
declare @pconsumer_id int = 110264
declare @ptype_insertion int = 110264
declare @peventId int = 0
declare @psessionid int = 0
declare @padhesion_catalog_id int = 1

****/
/* INSERT INTO panier_produit*/
INSERT INTO [Users]
      ([sessionweb_id]      
      ,[identite_id]      
	  ,[start_date]
      ,[adresseIP]
      ,[urlreferer]
      ,[structure_id]
      ,[browser]
      ,[application_path]
      ,[profil_acheteur_id]
      ,[queuingTicket])
VALUES 
(@psessionwebid, @pidentiteid, getdate(), @padresseIP, @purlreferer, @pStructureId, @pbrowser, @papplicationpath, @pprofilacheteurid, @pqueuingTicket)

DECLARE @myNewUser int = convert(int,SCOPE_IDENTITY());

DECLARE @msgTypeId int 
SELECT @msgTypeId = message_type_id FROM Messages_Types WHERE message_type_libelle_court='NEWSESSION'

INSERT INTO logs (user_id, log_date, message_type_id, message_text, url)
VALUES (@myNewUser, getdate(), @msgTypeId, 'new connexion', '.')

SELECT @myNewUser