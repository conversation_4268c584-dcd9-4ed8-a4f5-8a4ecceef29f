﻿/*
  declare @pFieldCodeId int = 4150
  declare @pTxtFR varchar(50) = 'd'
  declare @pTxtEN varchar(50) = 'd'
  declare @pTxtDE varchar(50) = 'd'
  declare @pTxtNL varchar(50) = 'd'

    declare @pTxtIT varchar(50) = ''
  declare @pTxtES varchar(50) = ''
  declare @pTxtPT varchar(50) = ''

  declare @pfieldCode varchar(50) = 'je_ne_c_pas_koi4'
  declare @pdescription varchar(50) = 'je_ne_c_pas_koi4'
  declare @areaId int = 4005
*/

--if @pfieldCode <> '' and @pTxtFR <> '' and @pTxtFR <> '' and @pTxtEN <> '' and @pTxtDE <> '' and @pTxtNL <> '' and @pFieldCodeId >0
if @pfieldCode <> '' and @pFieldCodeId >0
BEGIN

  declare @field_code varchar(max)
  set @field_code = (select fieldCode from translate_fieldsGlobalTranslation where id = @pFieldCodeId )


  update translate_fieldsGlobalTranslation set fieldCode= @pfieldCode, txt_fr= @pTxtFR, txt_en=@pTxtEN,txt_de=@pTxtDE, txt_nl=@pTxtNL, txt_it=@pTxtIT, txt_es=@pTxtES, txt_pt=@pTxtPT where fieldCode=@field_code and id = @pFieldCodeId

  update translate_fieldsCodesList set fieldSpecificCode = @pfieldCode, description = @pdescription, area_id=@areaId where fieldSpecificCode = @field_code and global_field_id = (select id from translate_fieldsGlobalTranslation where id =@pFieldCodeId ) 

END




 

