﻿INSERT INTO reference_lieu_web (web_picture, Lieu_physique_id, iindex, Lieu_id, web_posx, web_posy)
SELECT 
    ref.web_picture,
    @TargetLieuPhysiqueId AS Lieu_physique_id,
    rlp2.iindex,
    rlp2.Lieu_id,
    0 AS web_posx, 
    0 AS web_posy  
FROM (
    SELECT DISTINCT
        rsi2.iindex,
        FIRST_VALUE(rsi2.web_picture) OVER(PARTITION BY rsi2.iindex ORDER BY rsi2.ID DESC) AS web_picture
    FROM reference_lieu_web rsi2
    WHERE rsi2.Lieu_physique_id = @SourceLieuPhysiqueId
) AS ref
INNER JOIN reference_lieu_web rsi 
    ON rsi.iindex = ref.iindex AND rsi.web_picture = ref.web_picture
INNER JOIN reference_lieu_physique rlp 
    ON rlp.iindex = rsi.iindex
INNER JOIN reference_lieu_physique rlp2 
    ON rlp2.pos_x = rlp.pos_x AND rlp2.pos_y = rlp.pos_y
WHERE
    rsi.Lieu_physique_id = @SourceLieuPhysiqueId
    AND rlp.lieu_physique_id = @SourceLieuPhysiqueId
    AND rlp2.lieu_physique_id = @TargetLieuPhysiqueId;
