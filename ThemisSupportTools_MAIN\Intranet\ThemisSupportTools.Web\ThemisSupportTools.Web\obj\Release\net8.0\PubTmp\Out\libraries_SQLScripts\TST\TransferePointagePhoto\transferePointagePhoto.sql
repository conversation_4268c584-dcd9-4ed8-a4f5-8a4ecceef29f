﻿
/*
declare @pImageId int = 24
declare @pLieuPhysiqueIdSource int = 150001
declare @pLieuPhysiqueIdCible int = 250001
*/

/*INSERT INTO reference_salle_image
    SELECT @pImageId AS image_id, @pLieuPhysiqueIdCible AS lieu_physique_id, iindex, pos_x, pos_y 
    FROM reference_lieu_physique WHERE lieu_physique_id =@pLieuPhysiqueIdSource 
    AND iindex > 0*/
     

INSERT INTO reference_salle_image (image_id, lieu_physique_id, iindex, posx, posy)
SELECT ref.image_id, @TargetLieuPhysiqueId, rlp2.iindex, rsi.posx, rsi.posy
FROM (
    SELECT rsi2.iindex, MAX(rsi2.image_id) AS image_id 
    FROM reference_salle_image rsi2
    WHERE rsi2.lieu_physique_id = @SourceLieuPhysiqueId
    GROUP BY iindex
) AS ref
INNER JOIN reference_salle_image rsi ON rsi.iindex = ref.iindex AND rsi.image_id = ref.image_id
INNER JOIN reference_lieu_physique rlp ON rlp.iindex = rsi.iindex
INNER JOIN reference_lieu_physique rlp2 ON rlp2.pos_x = rlp.pos_x AND rlp2.pos_y = rlp.pos_y
WHERE LEFT(rlp.type_siege, 1) IN ('F', 'S') 
  AND LEFT(rlp2.type_siege, 1) IN ('F', 'S')
  AND rlp.lieu_physique_id = @SourceLieuPhysiqueId
  AND rsi.lieu_physique_id = @SourceLieuPhysiqueId 
  AND rlp2.lieu_physique_id = @TargetLieuPhysiqueId
  AND rlp2.rang > ' ' 
  AND rlp2.siege > ' ';