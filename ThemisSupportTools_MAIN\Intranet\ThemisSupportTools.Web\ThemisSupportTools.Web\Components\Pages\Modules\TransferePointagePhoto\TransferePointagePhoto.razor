﻿@page "/transfere-pointage-photo/{StructureId:int?}"
@using Core.Themis.Libraries.DTO.LieuPhysique;
@using Microsoft.AspNetCore.Components;
@using ThemisSupportTools.Web.Components.Pages.Modules.ChangeStructureButton

<Toasts class="p-3" Messages="toastMessages" Delay="6000" Placement="@toastsPlacement" />

<div class="container-fluid mt-4">

    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            @string.Format(Localizer["transfert_pointagephoto_titre_page"], structureName, StructureId)
        </div>
        <ChangeStructureButton ModuleName="transfere-pointage-photo" />
    </div>

    <div class="card shadow-sm">
        <div class="card-body p-4">
            @if (IsLoading)
            {
                <div class="d-flex justify-content-center my-5">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">@Localizer["transfert_pointagephoto_label_chargement"]</span>
                    </div>
                </div>
            }
            else
            {
                <!-- Onglets -->
                <ul class="nav nav-tabs mb-4" id="transfertTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="pointage-tab" data-bs-toggle="tab" data-bs-target="#pointage"
                                type="button" role="tab" aria-controls="pointage" aria-selected="true">
                            @Localizer["transfert_pointagephoto_titre_section"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="vignette-tab" data-bs-toggle="tab" data-bs-target="#vignette"
                                type="button" role="tab" aria-controls="vignette" aria-selected="false">
                            @Localizer["transfert_vignette_titre_section"]
                        </button>
                    </li>
                </ul>

                <!-- Contenu des onglets -->
                <div class="tab-content" id="transfertTabsContent">

                    <!-- === Onglet Pointage Photo === -->
                    <div class="tab-pane fade show active" id="pointage" role="tabpanel" aria-labelledby="pointage-tab">
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <!-- Sélection source -->
                                <div class="form-group mb-3">
                                    <label>@Localizer["transfert_pointagephoto_label_source"]</label>
                                    <select class="form-control" @bind="SelectedSourceLieuPhysiqueId">
                                        <option value="0">@Localizer["transfert_pointagephoto_option_source_defaut"]</option>
                                        @foreach (var lieu in LieuxPhysiquesWithImages)
                                        {
                                            <option value="@lieu.LieuNomId">@lieu.LieuPhysiqueNom (@lieu.LieuNomId)</option>
                                        }
                                    </select>
                                </div>
                                <!-- Sélection cible -->
                                <div class="form-group mb-3">
                                    <label>@Localizer["transfert_pointagephoto_label_cible"]</label>
                                    <select class="form-control" @bind="SelectedTargetLieuPhysiqueId">
                                        <option value="0">@Localizer["transfert_pointagephoto_option_cible_defaut"]</option>
                                        @foreach (var lieu in LieuxPhysiques)
                                        {
                                            if (lieu.LieuNomId != SelectedSourceLieuPhysiqueId)
                                            {
                                                <option value="@lieu.LieuNomId">@lieu.LieuPhysiqueNom (@lieu.LieuNomId)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <!-- Boutons Vérifier + Transférer + Générer XML -->
                                <div class="mb-3 d-flex gap-2 flex-wrap">
                                    <button type="button" class="btn btn-secondary" @onclick="CompareLieuxManuellement"
                                            disabled="@(SelectedSourceLieuPhysiqueId <= 0 || SelectedTargetLieuPhysiqueId <= 0)">
                                        @Localizer["transfert_pointagephoto_bouton_verifier"]
                                    </button>
                                    <button type="button" class="btn btn-primary" @onclick="ProcessTransfert"
                                            disabled="@(SelectedSourceLieuPhysiqueId <= 0 || SelectedTargetLieuPhysiqueId <= 0 || IncoherencesExistantes)">
                                        @Localizer["transfert_pointagephoto_bouton_transferer"]
                                    </button>
                                    <button type="button" class="btn btn-success" @onclick="GenerateCustomXmlFile" disabled="@(IsLoading || SelectedSourceLieuPhysiqueId <= 0)">
                                        @Localizer["transfert_pointagephoto_bouton_generer_xml"]
                                    </button>
                                </div>

                                <!-- Résultats de vérification -->
                                @if (IncoherencesSourceOnly.Any() || IncoherencesDestinationOnly.Any())
                                {
                                    <div class="alert alert-warning mt-3">
                                        <strong>@Localizer["transfert_resultats_incoherences"] </strong>
                                        <ul>
                                            @foreach (var item in IncoherencesSourceOnly.Take(10))
                                            {
                                                <li>@string.Format(Localizer["transfert_pointagephoto_incoherence_source"], item.Rang, item.Siege)</li>
                                            }
                                            @if (IncoherencesSourceOnly.Count > 10)
                                            {
                                                <li><strong>@string.Format(Localizer["transfert_pointagephoto_incoherence_source_autres"], IncoherencesSourceOnly.Count - 10)</strong></li>
                                            }

                                            @foreach (var item in IncoherencesDestinationOnly.Take(10))
                                            {
                                                <li>@string.Format(Localizer["transfert_pointagephoto_incoherence_cible"], item.Rang, item.Siege)</li>
                                            }
                                            @if (IncoherencesDestinationOnly.Count > 10)
                                            {
                                                <li><strong>@string.Format(Localizer["transfert_pointagephoto_incoherence_cible_autres"], IncoherencesDestinationOnly.Count - 10)</strong></li>
                                            }
                                        </ul>

                                    </div>
                                }

                            </div>
                        </div>
                    </div>

                    <!-- === Onglet Vignette === -->
                    <div class="tab-pane fade" id="vignette" role="tabpanel" aria-labelledby="vignette-tab">
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <!-- Sélection source vignette -->
                                <div class="form-group mb-3">
                                    <label>@Localizer["transfert_vignette_label_source"]</label>
                                    <select class="form-control" @bind="SelectedSourceVignetteLieuPhysiqueId">
                                        <option value="0">@Localizer["transfert_vignette_option_source_defaut"]</option>
                                        @foreach (var lieu in LieuxPhysiquesWithImages)
                                        {
                                            <option value="@lieu.LieuNomId">@lieu.LieuPhysiqueNom (@lieu.LieuNomId)</option>
                                        }
                                    </select>
                                </div>
                                <!-- Sélection cible vignette -->
                                <div class="form-group mb-3">
                                    <label>@Localizer["transfert_vignette_label_cible"]</label>
                                    <select class="form-control" @bind="SelectedTargetVignetteLieuPhysiqueId">
                                        <option value="0">@Localizer["transfert_vignette_option_cible_defaut"]</option>
                                        @foreach (var lieu in LieuxPhysiques)
                                        {
                                            if (lieu.LieuNomId != SelectedSourceVignetteLieuPhysiqueId)
                                            {
                                                <option value="@lieu.LieuNomId">@lieu.LieuPhysiqueNom (@lieu.LieuNomId)</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <!-- Boutons Vérifier + Transférer vignette + Générer XML vignette -->
                                <div class="mb-3 d-flex gap-2 flex-wrap">
                                    <button type="button" class="btn btn-secondary" @onclick="CompareLieuxManuellement"
                                            disabled="@(SelectedSourceVignetteLieuPhysiqueId <= 0 || SelectedTargetVignetteLieuPhysiqueId <= 0)">
                                        @Localizer["transfert_vignette_bouton_verifier"]
                                    </button>
                                    <button type="button" class="btn btn-primary" @onclick="ProcessVignetteTransfert"
                                            disabled="@(SelectedSourceVignetteLieuPhysiqueId <= 0 || SelectedTargetVignetteLieuPhysiqueId <= 0 || IncoherencesExistantes)">
                                        @Localizer["transfert_pointagephoto_bouton_transferer"]
                                    </button>
                                    <button type="button" class="btn btn-success" @onclick="GenerateCustomXmlFileVignette" disabled="@(IsLoading || SelectedSourceVignetteLieuPhysiqueId <= 0)">
                                        @Localizer["transfert_vignette_bouton_generer_xml"]
                                    </button>
                                </div>

                                <!-- Résultats de vérification vignette -->
                                @if (IncoherencesSourceOnly.Any() || IncoherencesDestinationOnly.Any())
                                {
                                    <div class="alert alert-warning mt-3">
                                        <strong>@Localizer["transfert_resultats_incoherences"]</strong>
                                        <ul>
                                            @foreach (var item in IncoherencesSourceOnly.Take(10))
                                            {
                                                <li>@string.Format(Localizer["transfert_pointagephoto_incoherence_source"], item.Rang, item.Siege)</li>
                                            }
                                            @if (IncoherencesSourceOnly.Count > 10)
                                            {
                                                <li><strong>@string.Format(Localizer["transfert_pointagephoto_incoherence_source_autres"], IncoherencesSourceOnly.Count - 10)</strong></li>
                                            }

                                            @foreach (var item in IncoherencesDestinationOnly.Take(10))
                                            {
                                                <li>@string.Format(Localizer["transfert_pointagephoto_incoherence_cible"], item.Rang, item.Siege)</li>
                                            }
                                            @if (IncoherencesDestinationOnly.Count > 10)
                                            {
                                                <li><strong>@string.Format(Localizer["transfert_pointagephoto_incoherence_cible_autres"], IncoherencesDestinationOnly.Count - 10)</strong></li>
                                            }
                                        </ul>

                                    </div>
                                }


                            </div>
                        </div>
                    </div>
                </div>

                <!-- === Bouton commun : Retour === -->
                <div class="row mt-4">
                    <div class="col-md-8 offset-md-2 d-flex justify-content-end">
                        <button type="button" class="btn btn-secondary" @onclick="RetourAccueil">
                            @Localizer["transfert_pointagephoto_bouton_retour"]
                        </button>
                    </div>
                </div>
            }
        </div>
    </div>
</div>