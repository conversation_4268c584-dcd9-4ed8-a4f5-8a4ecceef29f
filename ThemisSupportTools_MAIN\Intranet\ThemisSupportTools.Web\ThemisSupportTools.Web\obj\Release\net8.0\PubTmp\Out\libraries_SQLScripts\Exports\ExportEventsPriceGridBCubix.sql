﻿
SELECT m.manifestation_id as event_id, convert(int, seance_Id) as session_id,
	m.manifestation_nom as event_name, s.seance_date_deb as session_date,
	l.lieu_id as place_id, l.lieu_nom as place_name, l.lieu_rue1 as place_street1,
	l.lieu_rue2 as place_street2,
	l.lieu_rue3 as place_street3,
	l.lieu_rue4 as place_street4,
	l.lieu_cp as place_zip,
	l.lieu_ville as place_city
	
	--
/* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
  FROM seance s
  INNER JOIN manifestation m on m.manifestation_id = s.manifestation_id
  INNER JOIN lieu l on l.lieu_id = s.lieu_id
  WHERE seance_date_fin > getdate()
	AND (seance_date_deb<dateadd(year, 10, getdate())
	OR seance_date_deb >  Convert(datetime, '2099-01-01' )) -- date libre
    AND seance_verrouiller = 'N'
	  AND supprimer <> 'O'
ORDER BY m.manifestation_id, s.seance_date_deb


DECLARE cursorManifs CURSOR FOR
SELECT distinct[manifestation_id] /* parcourt des manifs ayant une seance a venir (mais pas dans 10 ans) */
  FROM [seance] 
  WHERE seance_date_fin > getdate()
 AND (seance_date_deb<dateadd(year, 10, getdate())
  OR seance_date_deb >  Convert(datetime, '2099-01-01' )) -- date libre
  AND seance_verrouiller = 'N'

DECLARE @MANIFID INT

DECLARE @tableSess TABLE (session_id int, seance_date datetime, price_id int, price_name varchar(50), category_id int, 
category_name varchar(50), amount decima(18,2))

OPEN cursorManifs;
FETCH  NEXT FROM cursorManifs INTO @MANIFID;
WHILE(@@FETCH_STATUS=0)
	BEGIN

		declare @sql varchar(max)
		set @sql = 'SELECT
		vts.seance_id ,
		s.seance_date_deb
		,tt.type_tarif_id, tt.type_tarif_nom
		,cat.categ_id, cat.categ_nom,
		vts.vts_grille1
		--,* 
		FROM valeur_tarif_stock' + LTRIM(STR(@MANIFID)) + ' vts
		INNER JOIN type_tarif tt on tt.type_tarif_id = vts.type_tarif_id
		INNER JOIN categorie cat on cat.categ_id = vts.categ_id
		INNER JOIN seance s on s.seance_Id = vts.seance_id
		WHERE s.seance_date_fin > GETDATE() and s.seance_cloturer =''N'' and s.seance_masquer = ''N'' 	 AND supprimer <> ''O''
		
		AND vts_v =( SELECT MAX(vts_v) FROM valeur_tarif_stock' + LTRIM(STR(@MANIFID)) + ' vts2
						WHERE vts2.tarif_logique_id=vts.tarif_logique_id
						and vts2.seance_id=vts.seance_id
						and vts2.categ_id= vts.categ_id
						and vts2.type_tarif_id= vts.type_tarif_id
		) and vts.vts_grille1>=0
		'

		print @sql

		INSERT INTO @tableSess (session_id, seance_date, price_id, price_name, category_id, category_name, amount)
		EXEC (@sql)
	FETCH  NEXT FROM cursorManifs INTO @MANIFID;
	
	END

CLOSE cursorManifs;
DEALLOCATE cursorManifs;

SELECT * FROM @tableSess ORDER BY session_id, price_name, category_name